#!/usr/bin/env python3
"""
Test the new simple AI system
"""
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

def test_simple_ai_system():
    """Test the simple AI system components"""
    print("🧪 Testing Simple AI System...")
    
    try:
        # Test 1: Ship outfit loading
        print("\n1. Testing ship outfit loading...")
        from game_objects.ships import get_ship_by_id
        
        scout = get_ship_by_id("scout")
        if scout and hasattr(scout, 'default_outfits'):
            outfits = scout.default_outfits
            print(f"   ✅ Scout has outfits: {outfits}")
        else:
            print("   ❌ Scout missing default_outfits")
            
        # Test 2: Simple AI import
        print("\n2. Testing Simple AI import...")
        from game_objects.simple_ai import SimpleAI, SIZE_POWER
        print(f"   ✅ SimpleAI imported, size powers: {SIZE_POWER}")
        
        # Test 3: Outfit registry
        print("\n3. Testing outfit registry...")
        from game_objects.standardized_outfits import OUTFITS_REGISTRY
        weapon_count = len([o for o in OUTFITS_REGISTRY.values() if o.category == "weapons"])
        print(f"   ✅ Found {weapon_count} weapons in registry")
        
        print("\n🎉 All tests passed! The simple AI system is ready.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_ai_system()
