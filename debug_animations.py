"""
Animation Debug Script
This will help us figure out exactly where the animation system is failing.
"""

import json
import os


def check_animation_endpoints_in_json():
    """Check what animation endpoints are actually in the JSON files."""
    print("=== Checking Animation Endpoints in JSON ===")
    
    # Check outfits_data.json
    if os.path.exists("outfits_data.json"):
        with open("outfits_data.json", 'r') as f:
            outfits_data = json.load(f)
        
        weapons_with_endpoints = 0
        ammo_with_endpoints = 0
        
        for outfit_id, outfit_data in outfits_data.items():
            category = outfit_data.get('category', '')
            if 'animation_endpoints' in outfit_data:
                endpoints = outfit_data['animation_endpoints']
                print(f"{outfit_id} ({category}):")
                for endpoint_name, endpoint_config in endpoints.items():
                    effect = endpoint_config.get('effect', 'unknown')
                    position = endpoint_config.get('position', 'unknown')
                    print(f"  {endpoint_name}: {effect} at {position}")
                
                if category == 'weapons':
                    weapons_with_endpoints += 1
                elif category == 'ammunition':
                    ammo_with_endpoints += 1
        
        print(f"Summary: {weapons_with_endpoints} weapons, {ammo_with_endpoints} ammo with endpoints")
        
        # Check specific weapons that should have endpoints
        test_weapons = ['laser_cannon', 'misslerack']
        test_ammo = ['missle', 'smartmissle']
        
        print("=== Testing Specific Items ===")
        for weapon_id in test_weapons:
            if weapon_id in outfits_data:
                has_endpoints = 'animation_endpoints' in outfits_data[weapon_id]
                print(f"Weapon {weapon_id}: {'✓' if has_endpoints else '✗'} has animation endpoints")
            else:
                print(f"Weapon {weapon_id}: ✗ not found in JSON")
        
        for ammo_id in test_ammo:
            if ammo_id in outfits_data:
                has_endpoints = 'animation_endpoints' in outfits_data[ammo_id]
                print(f"Ammo {ammo_id}: {'✓' if has_endpoints else '✗'} has animation endpoints")
            else:
                print(f"Ammo {ammo_id}: ✗ not found in JSON")


def check_effects_files():
    """Check what effect files we actually have."""
    print("=== Checking Effects Files ===")
    
    effects_dir = "assets/images/sprites/effects"
    if not os.path.exists(effects_dir):
        print(f"✗ Effects directory {effects_dir} doesn't exist!")
        return
    
    png_files = []
    json_files = []
    
    for filename in os.listdir(effects_dir):
        if filename.endswith('_spritesheet.png'):
            png_files.append(filename.replace('_spritesheet.png', ''))
        elif filename.endswith('_spritesheet.json'):
            json_files.append(filename.replace('_spritesheet.json', ''))
    
    print(f"PNG spritesheets: {png_files}")
    print(f"JSON metadata: {json_files}")
    
    # Check which effects are referenced in endpoints but missing files
    missing_effects = []
    if os.path.exists("outfits_data.json"):
        with open("outfits_data.json", 'r') as f:
            outfits_data = json.load(f)
        
        referenced_effects = set()
        for outfit_data in outfits_data.values():
            if 'animation_endpoints' in outfit_data:
                for endpoint_config in outfit_data['animation_endpoints'].values():
                    effect_name = endpoint_config.get('effect', '')
                    if effect_name:
                        referenced_effects.add(effect_name)
        
        print(f"Effects referenced in JSON: {sorted(referenced_effects)}")
        
        for effect in referenced_effects:
            if effect not in png_files:
                missing_effects.append(effect)
        
        if missing_effects:
            print(f"✗ Missing effect files: {missing_effects}")
        else:
            print("✓ All referenced effects have files")


if __name__ == "__main__":
    check_animation_endpoints_in_json()
    check_effects_files()
