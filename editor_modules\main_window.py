"""
Main Window for the Enhanced Content Editor
Manages the main interface and tab system
"""

import tkinter as tk
from tkinter import ttk, messagebox

from .outfit_editors.weapons_editor import WeaponsEditor
from .outfit_editors.ammunition_editor import AmmunitionEditor
from .outfit_editors.defense_editor import DefenseEditor
from .outfit_editors.engines_editor import EnginesEditor
from .outfit_editors.electronics_editor import ElectronicsEditor
from .outfit_editors.utility_editor import UtilityEditor
from .ship_editor import ShipEditor

class MainEditorWindow:
    """Main window for the enhanced content editor."""
    
    def __init__(self, root, data_manager):
        self.root = root
        self.data_manager = data_manager
        
        self.setup_window()
        self.setup_ui()
        self.load_data()
    
    def setup_window(self):
        """Setup the main window properties."""
        self.root.title("EV Py - Enhanced Content Editor - Modular")
        self.root.geometry("1600x1000")  # Increased size to prevent button cutoff

        # Make window resizable
        self.root.resizable(True, True)

        # Set minimum window size
        self.root.minsize(1400, 900)
        
        # Setup menu bar
        self.setup_menu()
    
    def setup_menu(self):
        """Setup the menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Save All Outfits", command=self.save_all_outfits)
        file_menu.add_command(label="Save All Ships", command=self.save_all_ships)
        file_menu.add_separator()
        file_menu.add_command(label="Auto-Save", command=self.auto_save)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def setup_ui(self):
        """Setup the main UI with tabs."""
        # Create notebook for categories
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Initialize editors
        self.editors = {}
        
        # Create outfit editors
        self.editors['weapons'] = WeaponsEditor(self.notebook, self.data_manager)
        self.editors['ammunition'] = AmmunitionEditor(self.notebook, self.data_manager)
        self.editors['defense'] = DefenseEditor(self.notebook, self.data_manager)
        self.editors['engines'] = EnginesEditor(self.notebook, self.data_manager)
        self.editors['electronics'] = ElectronicsEditor(self.notebook, self.data_manager)
        self.editors['utility'] = UtilityEditor(self.notebook, self.data_manager)
        
        # Create ship editor
        self.editors['ships'] = ShipEditor(self.notebook, self.data_manager)
        
        # Add tabs to notebook
        for name, editor in self.editors.items():
            self.notebook.add(editor.frame, text=name.title())
    
    def load_data(self):
        """Load data into all editors."""
        for editor in self.editors.values():
            editor.load_data()
    
    def save_all_outfits(self):
        """Save all outfit data."""
        try:
            success = self.data_manager.save_outfits_data()
            if success:
                messagebox.showinfo("Success", "All outfits saved successfully!")
            else:
                messagebox.showerror("Error", "Failed to save outfits")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save outfits: {e}")
    
    def save_all_ships(self):
        """Save all ship data."""
        try:
            success = self.data_manager.save_ships_data()
            if success:
                messagebox.showinfo("Success", "All ships saved successfully!")
            else:
                messagebox.showerror("Error", "Failed to save ships")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save ships: {e}")
    
    def auto_save(self):
        """Auto-save all data."""
        try:
            self.data_manager.auto_save()
            messagebox.showinfo("Success", "Auto-save completed!")
        except Exception as e:
            messagebox.showerror("Error", f"Auto-save failed: {e}")
    
    def show_about(self):
        """Show about dialog."""
        about_text = """
Enhanced Content Editor - Modular Version

A comprehensive editor for Escape Velocity Py game content.

Features:
- Modular architecture for easy maintenance
- Comprehensive outfit editing (weapons, ammunition, defense, engines, electronics, utility)
- Ship editing with full property support
- JSON export/import functionality
- Auto-save capabilities

Version: 2.0 (Refactored)
        """
        messagebox.showinfo("About", about_text.strip())
    
    def refresh_all_dropdowns(self):
        """Refresh all dropdowns in editors that depend on outfit data."""
        try:
            # Refresh ship editor dropdowns (they depend on outfit data)
            if 'ships' in self.editors:
                ship_editor = self.editors['ships']
                if hasattr(ship_editor, 'refresh_outfit_dropdowns'):
                    ship_editor.refresh_outfit_dropdowns()
                    print("Main Window: Refreshed ship editor dropdowns")

            # Refresh ammunition editor launcher dropdown (depends on weapon data)
            if 'ammunition' in self.editors:
                ammo_editor = self.editors['ammunition']
                if hasattr(ammo_editor, 'refresh_launcher_dropdown'):
                    ammo_editor.refresh_launcher_dropdown()
                    print("Main Window: Refreshed ammunition editor launcher dropdown")

            print("Main Window: Completed dropdown refresh cycle")

        except Exception as e:
            print(f"Main Window: Error refreshing dropdowns: {e}")
