"""
Ammunition Editor for the Enhanced Content Editor
Handles editing of ammunition outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor
from .ui_components import StandardParameterGrid, ParameterLoader
from .animation_endpoint_ui import AmmunitionAnimationEndpoints

class AmmunitionEditor(BaseOutfitEditor):
    """Editor for ammunition outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "ammunition")
    
    def setup_editor_ui(self, parent):
        """Setup the ammunition editor interface with improved horizontal layout."""
        # Basic Properties Grid - 2 columns for better space usage
        self.basic_grid = StandardParameterGrid(parent, "Basic Properties", columns=2)
        self.basic_grid.add_string_field("Name", "name")
        self.basic_grid.add_int_field("Cost", "cost", 10, 10000)
        self.basic_grid.add_int_field("Quantity", "quantity", 1, 100)
        # File fields automatically span full width
        self.basic_grid.add_file_field("Outfitter Icon", "outfitter_icon")
        self.basic_grid.add_file_field("Outfitter Image", "outfitter_image")
        self.basic_grid.add_file_field("Projectile Sprite", "projectile_sprite",
                                      [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")])
        # Multi-select field automatically spans full width
        self.basic_grid.add_multi_select_field("Compatible Launchers", "compatible_launchers", self._get_available_launchers)
        # Set up the launcher selection callback properly - DISABLE auto-callback to prevent conflicts
        # self.basic_grid.set_multiselect_change_callback(self._on_launcher_selection_change)

        # Bind the listbox selection event directly for controlled feedback
        launcher_listbox = self.basic_grid.vars.get('compatible_launchers')
        if launcher_listbox:
            # Remove any existing bindings first
            launcher_listbox.unbind('<<ListboxSelect>>')
            # Add our controlled binding
            launcher_listbox.bind('<<ListboxSelect>>',
                                lambda e: self._on_launcher_selection_change('compatible_launchers'))
        self.basic_grid.pack(fill=tk.X, padx=5, pady=5)

        # Combat Properties Grid - 3 columns for compact layout
        self.combat_grid = StandardParameterGrid(parent, "Combat Properties", columns=3)
        self.combat_grid.add_int_field("Shield Damage", "shield_damage", 1, 1000)
        self.combat_grid.add_int_field("Armor Damage", "armor_damage", 1, 1000)
        self.combat_grid.add_int_field("Range", "range", 50, 2000)
        self.combat_grid.add_int_field("Projectile Speed", "projectile_speed", 50, 1000)
        self.combat_grid.add_float_field("Tracking Strength", "tracking_strength", 0.0, 1.0, 0.1)
        self.combat_grid.pack(fill=tk.X, padx=5, pady=5)

        # Behavior Properties Grid - 3 columns for compact layout
        self.behavior_grid = StandardParameterGrid(parent, "Behavior Properties", columns=3)
        self.behavior_grid.add_combo_field("Behavior", "projectile_behavior", ["dumbfire", "guided", "beam", "delayed", "proximity"])
        self.behavior_grid.add_float_field("Delay Time", "delay_time", 0.0, 10.0, 0.1)
        self.behavior_grid.add_int_field("Explosion Radius", "explosion_radius", 0, 100)
        
        # Re-enable behavior change callback with protection
        behavior_var = self.behavior_grid.vars['projectile_behavior']
        behavior_var.trace('w', self._on_behavior_change)
        
        self.behavior_grid.pack(fill=tk.X, padx=5, pady=5)
        
        # Animation Endpoints
        self.animation_endpoints = AmmunitionAnimationEndpoints(parent)
        self.animation_endpoints.pack(fill=tk.X, padx=5, pady=5)
        
        # Description
        desc_frame = ttk.LabelFrame(parent, text="Description")
        desc_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.description_text = tk.Text(desc_frame, height=3, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=5)

        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        ttk.Button(save_frame, text="Save Ammunition", command=self.save_item).pack(side=tk.RIGHT, padx=5)
    
    def load_item_into_editor(self, ammo):
        """Load ammunition data into the editor."""
        super().load_item_into_editor(ammo)
        
        # Force reload from JSON to get fresh data
        if hasattr(ammo, 'id') or hasattr(ammo, 'name'):
            outfit_id = getattr(ammo, 'id', ammo.name.lower().replace(' ', '_'))
            
            # Check multiple possible ID formats
            possible_ids = [
                outfit_id,
                ammo.name.lower().replace(' ', ''),  # "smartmissile" vs "smartmissle"
                ammo.name.lower().replace(' ', '_'),  # "smart_missile"
                getattr(ammo, 'id', ''),  # Original ID
            ]
            
            for test_id in possible_ids:
                if test_id and self.data_manager.reload_single_outfit(test_id):
                    # Get the refreshed outfit object
                    refreshed_outfits = self.data_manager.get_outfits_by_category(self.category_name)
                    for outfit_obj in refreshed_outfits.values():
                        if getattr(outfit_obj, 'id', outfit_obj.name.lower().replace(' ', '_')) == test_id:
                            ammo = outfit_obj
                            self.current_outfit = ammo
                            break
                    break
        
        # Load using standardized parameter loader
        ParameterLoader.load_outfit_parameters(ammo, self.basic_grid)
        ParameterLoader.load_outfit_parameters(ammo, self.combat_grid)
        ParameterLoader.load_outfit_parameters(ammo, self.behavior_grid)

        # Force refresh launcher dropdown to ensure it has current data
        self.refresh_launcher_dropdown()

        # Force reload the compatible launchers specifically
        launcher_listbox = self.basic_grid.vars.get('compatible_launchers')
        if launcher_listbox:
            compatible_launchers = getattr(ammo, 'compatible_launchers', [])
            print(f"DEBUG: Loading compatible launchers for {ammo.name}: {compatible_launchers}")

            # Clear selections first
            launcher_listbox.selection_clear(0, tk.END)

            # Select the compatible launchers
            for i in range(launcher_listbox.size()):
                launcher_id = launcher_listbox.get(i)
                if launcher_id in compatible_launchers:
                    launcher_listbox.selection_set(i)
                    print(f"DEBUG: Selected launcher {launcher_id} at index {i}")

        # Force update multi-select status after loading
        self.basic_grid._update_multiselect_status('compatible_launchers')
        
        # Load animation endpoints
        animation_endpoints = getattr(ammo, 'animation_endpoints', {})
        self.animation_endpoints.load_endpoints(animation_endpoints)
        
        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(ammo, 'description', ''))
    
    def save_item(self):
        """Save the current ammunition with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No ammunition selected to save")
            return

        try:
            # Save using standardized parameter loader
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.basic_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.combat_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.behavior_grid)

            # CRITICAL FIX: Manually save compatible launchers to ensure they're captured
            launcher_listbox = self.basic_grid.vars.get('compatible_launchers')
            if launcher_listbox:
                selected_launchers = [launcher_listbox.get(i) for i in launcher_listbox.curselection()]
                self.current_outfit.compatible_launchers = selected_launchers
                print(f"DEBUG: Manually saved compatible launchers: {selected_launchers}")

            # Save animation endpoints
            animation_endpoints = self.animation_endpoints.save_endpoints()
            if animation_endpoints:
                self.current_outfit.animation_endpoints = animation_endpoints
            elif hasattr(self.current_outfit, 'animation_endpoints'):
                delattr(self.current_outfit, 'animation_endpoints')

            # Save description
            self.current_outfit.description = self.description_text.get(1.0, tk.END).strip()

            super().save_item()

            # CRITICAL FIX: Force reload the item to refresh UI with saved data
            self.load_item_into_editor(self.current_outfit)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save ammunition: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new ammunition instance."""
        class SimpleAmmo:
            def __init__(self, id, name):
                # Basic properties
                self.id = id
                self.name = name
                self.category = "ammunition"
                self.cost = 500
                self.compatible_launchers = []
                self.quantity = 10
                self.outfitter_icon = ""
                self.outfitter_image = ""
                self.projectile_sprite = ""
                # Combat properties  
                self.shield_damage = 40
                self.armor_damage = 60
                self.range = 600
                self.projectile_speed = 300
                self.tracking_strength = 0.0
                # Behavior properties
                self.projectile_behavior = "dumbfire"
                self.delay_time = 0.0
                self.explosion_radius = 20
                self.description = ""

        return SimpleAmmo(item_id, item_id.replace('_', ' ').title())
    
    def _get_available_launchers(self):
        """Get list of available launcher weapons."""
        launchers = []
        weapons = self.data_manager.get_outfits_by_category('weapons')
        for weapon_id, weapon in weapons.items():
            if getattr(weapon, 'uses_ammo', False):
                launchers.append(weapon_id)
        print(f"DEBUG: Found {len(launchers)} launchers: {launchers}")
        return launchers

    def refresh_launcher_dropdown(self):
        """Refresh the launcher dropdown with current launcher weapons."""
        try:
            # Get the listbox for compatible launchers
            launcher_listbox = self.basic_grid.vars.get('compatible_launchers')
            if launcher_listbox:
                # Store current selections
                current_selections = [launcher_listbox.get(i) for i in launcher_listbox.curselection()]

                # Get fresh launcher list
                launchers = self._get_available_launchers()

                # Clear and repopulate
                launcher_listbox.delete(0, tk.END)
                for launcher in launchers:
                    launcher_listbox.insert(tk.END, launcher)

                # Restore selections that still exist
                for i, launcher in enumerate(launchers):
                    if launcher in current_selections:
                        launcher_listbox.selection_set(i)

                # Update status display
                self.basic_grid._update_multiselect_status('compatible_launchers')

                print(f"Ammunition Editor: Refreshed launcher dropdown with {len(launchers)} launchers")

        except Exception as e:
            print(f"Error refreshing launcher dropdown: {e}")
    
    def _on_launcher_selection_change(self, var_name):
        """Handle launcher selection change with auto-save protection."""
        if hasattr(self, '_updating_launchers') and self._updating_launchers:
            return  # Prevent infinite loops

        if not self.current_outfit:
            return

        # Add a small delay to prevent rapid-fire events
        if hasattr(self, '_launcher_change_pending'):
            return

        try:
            self._updating_launchers = True
            self._launcher_change_pending = True

            # Get selected launchers
            listbox = self.basic_grid.vars.get(var_name)
            if listbox:
                selected_launchers = [listbox.get(i) for i in listbox.curselection()]

                # Only update if the selection actually changed
                current_launchers = getattr(self.current_outfit, 'compatible_launchers', [])
                if selected_launchers != current_launchers:
                    # Update the outfit
                    self.current_outfit.compatible_launchers = selected_launchers

                    # Auto-save (with protection already in data_manager)
                    self.data_manager.auto_save()

                    print(f"Ammunition: Updated compatible launchers for {self.current_outfit.name}: {selected_launchers}")

                # Update status display
                self.basic_grid._update_multiselect_status(var_name)

        except Exception as e:
            print(f"Error updating launcher selection: {e}")
        finally:
            self._updating_launchers = False
            # Clear the pending flag after a short delay
            if hasattr(self, 'parent'):
                self.parent.after(100, lambda: setattr(self, '_launcher_change_pending', False))
    
    def _on_behavior_change(self, *args):
        """Handle behavior change with auto-save protection."""
        if hasattr(self, '_updating_behavior') and self._updating_behavior:
            return  # Prevent infinite loops
        
        if not self.current_outfit:
            return
        
        try:
            self._updating_behavior = True
            
            # Get behavior value
            behavior_var = self.behavior_grid.vars.get('projectile_behavior')
            if behavior_var:
                behavior = behavior_var.get()
                
                # Update the outfit
                self.current_outfit.projectile_behavior = behavior
                
                # Auto-save (with protection already in data_manager)
                self.data_manager.auto_save()
                
                print(f"Ammunition: Updated behavior for {self.current_outfit.name}: {behavior}")
                
        except Exception as e:
            print(f"Error updating behavior: {e}")
        finally:
            self._updating_behavior = False
