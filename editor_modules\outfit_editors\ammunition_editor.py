"""
Ammunition Editor for the Enhanced Content Editor
Handles editing of ammunition outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor
from .ui_components import StandardParameterGrid, ParameterLoader
from .animation_endpoint_ui import AmmunitionAnimationEndpoints


class SimpleLauncherSelector:
    """Simple, reliable launcher selector widget."""

    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        self.selected_launchers = []

        # Create the frame
        self.frame = ttk.LabelFrame(parent, text="Compatible Launchers")

        # Create the UI
        self.setup_ui()

    def setup_ui(self):
        """Setup the launcher selector UI."""
        # Instructions
        ttk.Label(self.frame, text="Select which launchers can use this ammunition:",
                 font=('Arial', 9, 'italic')).pack(anchor=tk.W, padx=5, pady=2)

        # Container for checkboxes
        self.checkbox_frame = ttk.Frame(self.frame)
        self.checkbox_frame.pack(fill=tk.X, padx=5, pady=5)

        # Status label
        self.status_label = ttk.Label(self.frame, text="No launchers selected",
                                     foreground="gray", font=('Arial', 8))
        self.status_label.pack(anchor=tk.W, padx=5, pady=2)

        self.checkbox_vars = {}
        self.refresh_launchers()

    def refresh_launchers(self):
        """Refresh the list of available launchers."""
        # Clear existing checkboxes
        for widget in self.checkbox_frame.winfo_children():
            widget.destroy()
        self.checkbox_vars.clear()

        # Get available launchers
        launchers = self.get_available_launchers()

        if not launchers:
            ttk.Label(self.checkbox_frame, text="No launchers found. Create launcher weapons first.",
                     foreground="orange").pack(anchor=tk.W)
            return

        # Create checkboxes in a grid (3 columns)
        row = 0
        col = 0
        for launcher_id in launchers:
            var = tk.BooleanVar()
            var.trace('w', lambda *args, lid=launcher_id: self.on_launcher_changed(lid))

            checkbox = ttk.Checkbutton(self.checkbox_frame, text=launcher_id, variable=var)
            checkbox.grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)

            self.checkbox_vars[launcher_id] = var

            col += 1
            if col >= 3:
                col = 0
                row += 1

        self.update_status()

    def get_available_launchers(self):
        """Get list of available launcher weapons."""
        launchers = []
        weapons = self.data_manager.get_outfits_by_category('weapons')
        for weapon_id, weapon in weapons.items():
            if getattr(weapon, 'uses_ammo', False):
                launchers.append(weapon_id)
        return sorted(launchers)

    def on_launcher_changed(self, launcher_id):
        """Handle launcher checkbox change."""
        # Update selected launchers list
        self.selected_launchers = [lid for lid, var in self.checkbox_vars.items() if var.get()]
        self.update_status()

        # Notify parent if callback is set
        if hasattr(self, 'change_callback'):
            self.change_callback(self.selected_launchers)

    def update_status(self):
        """Update the status label."""
        count = len(self.selected_launchers)
        if count == 0:
            self.status_label.config(text="No launchers selected", foreground="gray")
        elif count == 1:
            self.status_label.config(text=f"✓ Selected: {self.selected_launchers[0]}", foreground="green")
        else:
            self.status_label.config(text=f"✓ {count} launchers selected: {', '.join(self.selected_launchers[:2])}{'...' if count > 2 else ''}",
                                   foreground="green")

    def set_selected_launchers(self, launcher_list):
        """Set the selected launchers."""
        self.selected_launchers = launcher_list if launcher_list else []

        # Update checkboxes
        for launcher_id, var in self.checkbox_vars.items():
            var.set(launcher_id in self.selected_launchers)

        self.update_status()

    def get_selected_launchers(self):
        """Get the currently selected launchers."""
        return self.selected_launchers.copy()

    def set_change_callback(self, callback):
        """Set callback for when selection changes."""
        self.change_callback = callback

    def pack(self, **kwargs):
        """Pack the frame."""
        self.frame.pack(**kwargs)

class AmmunitionEditor(BaseOutfitEditor):
    """Editor for ammunition outfits."""

    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "ammunition")

    def setup_editor_ui(self, parent):
        """Setup the ammunition editor interface with simple, reliable launcher selection."""
        # Basic Properties Grid - 2 columns for better space usage
        self.basic_grid = StandardParameterGrid(parent, "Basic Properties", columns=2)
        self.basic_grid.add_string_field("Name", "name")
        self.basic_grid.add_int_field("Cost", "cost", 10, 10000)
        self.basic_grid.add_int_field("Quantity", "quantity", 1, 100)
        # File fields automatically span full width
        self.basic_grid.add_file_field("Outfitter Icon", "outfitter_icon")
        self.basic_grid.add_file_field("Outfitter Image", "outfitter_image")
        self.basic_grid.add_file_field("Projectile Sprite", "projectile_sprite",
                                      [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")])
        self.basic_grid.pack(fill=tk.X, padx=5, pady=5)

        # Simple Launcher Selector - completely separate from the complex grid system
        self.launcher_selector = SimpleLauncherSelector(parent, self.data_manager)
        self.launcher_selector.set_change_callback(self.on_launcher_selection_changed)
        self.launcher_selector.pack(fill=tk.X, padx=5, pady=5)

        # Combat Properties Grid - 3 columns for compact layout
        self.combat_grid = StandardParameterGrid(parent, "Combat Properties", columns=3)
        self.combat_grid.add_int_field("Shield Damage", "shield_damage", 1, 1000)
        self.combat_grid.add_int_field("Armor Damage", "armor_damage", 1, 1000)
        self.combat_grid.add_int_field("Range", "range", 50, 2000)
        self.combat_grid.add_int_field("Projectile Speed", "projectile_speed", 50, 1000)
        self.combat_grid.add_float_field("Tracking Strength", "tracking_strength", 0.0, 1.0, 0.1)
        self.combat_grid.pack(fill=tk.X, padx=5, pady=5)

        # Behavior Properties Grid - 3 columns for compact layout
        self.behavior_grid = StandardParameterGrid(parent, "Behavior Properties", columns=3)
        self.behavior_grid.add_combo_field("Behavior", "projectile_behavior", ["dumbfire", "guided", "beam", "delayed", "proximity"])
        self.behavior_grid.add_float_field("Delay Time", "delay_time", 0.0, 10.0, 0.1)
        self.behavior_grid.add_int_field("Explosion Radius", "explosion_radius", 0, 100)

        # Re-enable behavior change callback with protection
        behavior_var = self.behavior_grid.vars['projectile_behavior']
        behavior_var.trace('w', self._on_behavior_change)

        self.behavior_grid.pack(fill=tk.X, padx=5, pady=5)

        # Animation Endpoints
        self.animation_endpoints = AmmunitionAnimationEndpoints(parent)
        self.animation_endpoints.pack(fill=tk.X, padx=5, pady=5)

        # Description
        desc_frame = ttk.LabelFrame(parent, text="Description")
        desc_frame.pack(fill=tk.X, padx=5, pady=5)

        self.description_text = tk.Text(desc_frame, height=3, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=5)

        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        ttk.Button(save_frame, text="Save Ammunition", command=self.save_item).pack(side=tk.RIGHT, padx=5)

    def on_launcher_selection_changed(self, selected_launchers):
        """Handle launcher selection change - simple and direct."""
        if not self.current_outfit:
            return

        print(f"SIMPLE: Launcher selection changed to: {selected_launchers}")

        # Update the outfit directly
        self.current_outfit.compatible_launchers = selected_launchers

        # Auto-save
        self.data_manager.auto_save()

        print(f"SIMPLE: Saved compatible launchers: {getattr(self.current_outfit, 'compatible_launchers', [])}")
    
    def load_item_into_editor(self, ammo):
        """Load ammunition data into the editor with simple launcher loading."""
        super().load_item_into_editor(ammo)

        print(f"SIMPLE: Loading ammo {ammo.name}")

        # Load using standardized parameter loader (for basic properties)
        ParameterLoader.load_outfit_parameters(ammo, self.basic_grid)
        ParameterLoader.load_outfit_parameters(ammo, self.combat_grid)
        ParameterLoader.load_outfit_parameters(ammo, self.behavior_grid)

        # Load compatible launchers using the simple selector
        compatible_launchers = getattr(ammo, 'compatible_launchers', [])
        print(f"SIMPLE: Loading compatible launchers: {compatible_launchers}")
        print(f"SIMPLE: Ammo object attributes: {[attr for attr in dir(ammo) if not attr.startswith('_')]}")
        print(f"SIMPLE: Ammo object dict: {getattr(ammo, '__dict__', 'No __dict__')}")

        # Refresh launcher list first to ensure we have current data
        self.launcher_selector.refresh_launchers()

        # Set the selected launchers
        self.launcher_selector.set_selected_launchers(compatible_launchers)

        # Load animation endpoints
        animation_endpoints = getattr(ammo, 'animation_endpoints', {})
        self.animation_endpoints.load_endpoints(animation_endpoints)

        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(ammo, 'description', ''))
    
    def save_item(self):
        """Save the current ammunition with simple launcher saving."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No ammunition selected to save")
            return

        try:
            print(f"SIMPLE: Saving ammo {self.current_outfit.name}")

            # Save using standardized parameter loader (for basic properties)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.basic_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.combat_grid)
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.behavior_grid)

            # Save compatible launchers using the simple selector
            selected_launchers = self.launcher_selector.get_selected_launchers()
            self.current_outfit.compatible_launchers = selected_launchers
            print(f"SIMPLE: Saving compatible launchers: {selected_launchers}")

            # Save animation endpoints
            animation_endpoints = self.animation_endpoints.save_endpoints()
            if animation_endpoints:
                self.current_outfit.animation_endpoints = animation_endpoints
            elif hasattr(self.current_outfit, 'animation_endpoints'):
                delattr(self.current_outfit, 'animation_endpoints')

            # Save description
            self.current_outfit.description = self.description_text.get(1.0, tk.END).strip()

            super().save_item()

            print(f"SIMPLE: Saved! Final compatible_launchers: {getattr(self.current_outfit, 'compatible_launchers', [])}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save ammunition: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new ammunition instance."""
        class SimpleAmmo:
            def __init__(self, id, name):
                # Basic properties
                self.id = id
                self.name = name
                self.category = "ammunition"
                self.cost = 500
                self.compatible_launchers = []
                self.quantity = 10
                self.outfitter_icon = ""
                self.outfitter_image = ""
                self.projectile_sprite = ""
                # Combat properties  
                self.shield_damage = 40
                self.armor_damage = 60
                self.range = 600
                self.projectile_speed = 300
                self.tracking_strength = 0.0
                # Behavior properties
                self.projectile_behavior = "dumbfire"
                self.delay_time = 0.0
                self.explosion_radius = 20
                self.description = ""

        return SimpleAmmo(item_id, item_id.replace('_', ' ').title())
    
    def _on_behavior_change(self, *args):
        """Handle behavior change with auto-save protection."""
        if hasattr(self, '_updating_behavior') and self._updating_behavior:
            return  # Prevent infinite loops
        
        if not self.current_outfit:
            return
        
        try:
            self._updating_behavior = True
            
            # Get behavior value
            behavior_var = self.behavior_grid.vars.get('projectile_behavior')
            if behavior_var:
                behavior = behavior_var.get()
                
                # Update the outfit
                self.current_outfit.projectile_behavior = behavior
                
                # Auto-save (with protection already in data_manager)
                self.data_manager.auto_save()
                
                print(f"Ammunition: Updated behavior for {self.current_outfit.name}: {behavior}")
                
        except Exception as e:
            print(f"Error updating behavior: {e}")
        finally:
            self._updating_behavior = False
