"""
Complete Animation Endpoints Setup Script
This will add animation endpoints to all remaining ships and verify everything is working.
"""

import json
import os

def complete_animation_setup():
    print("=== COMPLETING ANIMATION ENDPOINTS SETUP ===")
    
    # Load ships data
    with open('ships_data.json', 'r') as f:
        ships_data = json.load(f)
    
    # Ships that need engine trail effects
    ship_updates = {
        # ship_id: [x_offset, y_offset] based on size
        'light_fighter': [0, 15],    # small
        'freighter': [0, 20],        # medium (already added)
        'kraken': [0, 15],           # small (already added)
        'corvette': [0, 25],         # large (already added)
        'heavyfighter': [0, 20],     # medium (already added)
        'gunship': [0, 20],          # medium
        'passengerliner': [0, 20],   # medium
        'bulkcarrier': [0, 25],      # large
        'frigate': [0, 25],          # large
        'heavyfreighter': [0, 25],   # large
        'battleship': [0, 30],       # capital
        'carrier': [0, 30],          # capital
        'cruiser': [0, 30],          # capital
        'destroyer': [0, 30],        # capital
        'courier': [0, 15],          # small
    }
    
    updated_ships = 0
    
    for ship_id, position in ship_updates.items():
        if ship_id in ships_data:
            ship = ships_data[ship_id]
            
            # Add animation endpoints if not present
            if 'animation_endpoints' not in ship:
                ship['animation_endpoints'] = {}
            
            # Add engine trail if not present
            if 'engine_trail' not in ship['animation_endpoints']:
                ship['animation_endpoints']['engine_trail'] = {
                    'effect': 'engine_trail',
                    'position': position,
                    'trigger': 'on_thrust'
                }
                print(f"✓ Added engine trail to {ship_id} at position {position}")
                updated_ships += 1
            else:
                print(f"- {ship_id} already has engine trail")
    
    # Save updated ships data
    with open('ships_data.json', 'w') as f:
        json.dump(ships_data, f, indent=2)
    
    print(f"\n✅ Updated {updated_ships} ships with engine trail effects")
    
    # Now run a verification check
    print("\n=== VERIFICATION CHECK ===")
    check_animation_endpoints()

def check_animation_endpoints():
    """Verify all animation endpoints are properly configured."""
    
    # Check outfits
    if os.path.exists("outfits_data.json"):
        with open("outfits_data.json", 'r') as f:
            outfits_data = json.load(f)
        
        weapons_with_endpoints = 0
        ammo_with_endpoints = 0
        referenced_effects = set()
        
        print("OUTFIT ENDPOINTS:")
        for outfit_id, outfit_data in outfits_data.items():
            category = outfit_data.get('category', '')
            if 'animation_endpoints' in outfit_data:
                endpoints = outfit_data['animation_endpoints']
                print(f"  {outfit_id} ({category}): {list(endpoints.keys())}")
                
                for endpoint_config in endpoints.values():
                    effect = endpoint_config.get('effect', 'unknown')
                    referenced_effects.add(effect)
                
                if category == 'weapons':
                    weapons_with_endpoints += 1
                elif category == 'ammunition':
                    ammo_with_endpoints += 1
        
        print(f"Summary: {weapons_with_endpoints} weapons, {ammo_with_endpoints} ammo with endpoints")
    
    # Check ships
    if os.path.exists("ships_data.json"):
        with open("ships_data.json", 'r') as f:
            ships_data = json.load(f)
        
        ships_with_endpoints = 0
        ship_effects = set()
        
        print("\nSHIP ENDPOINTS:")
        for ship_id, ship_data in ships_data.items():
            if 'animation_endpoints' in ship_data:
                endpoints = ship_data['animation_endpoints']
                print(f"  {ship_id}: {list(endpoints.keys())}")
                
                for endpoint_config in endpoints.values():
                    effect = endpoint_config.get('effect', 'unknown')
                    ship_effects.add(effect)
                
                ships_with_endpoints += 1
        
        print(f"Summary: {ships_with_endpoints} ships with endpoints")
        
        # Combine all effects
        all_effects = referenced_effects.union(ship_effects)
        print(f"\nAll effects referenced: {sorted(all_effects)}")
    
    print("\n✅ ANIMATION SYSTEM SETUP COMPLETE!")
    print("Your game should now show:")
    print("- Muzzle flashes when weapons fire")
    print("- Engine trails when ships thrust")
    print("- Explosion effects when projectiles hit")

if __name__ == "__main__":
    complete_animation_setup()
