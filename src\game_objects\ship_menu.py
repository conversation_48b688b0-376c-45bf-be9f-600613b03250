"""
Modern Ship Menu System for Escape Velocity Py.
This module handles the player's ship management interface with modern UI design.
"""

import pygame as pg
import math
from game_objects.sprite_manager import load_sprite

# Modern UI Color Scheme
BACKGROUND_COLOR = (12, 15, 25)  # Deep space blue
PANEL_COLOR = (25, 30, 45)  # Dark panel
PANEL_BORDER = (60, 70, 90)  # Panel borders
HIGHLIGHT_COLOR = (45, 55, 80)  # Highlighted elements
ACCENT_COLOR = (100, 150, 255)  # Blue accent
SECONDARY_ACCENT = (255, 180, 100)  # Orange accent

TEXT_COLOR = (220, 225, 235)  # Light text
TITLE_COLOR = (255, 255, 255)  # White titles
SUBTITLE_COLOR = (180, 190, 210)  # Subtitle text
STAT_GOOD_COLOR = (100, 255, 150)  # Green for good stats
STAT_BAD_COLOR = (255, 120, 120)  # Red for bad stats
STAT_NEUTRAL_COLOR = (255, 220, 100)  # Yellow for neutral
STAT_CRITICAL_COLOR = (255, 80, 80)  # Critical red

BUTTON_COLOR = (50, 70, 120)  # Button background
BUTTON_HOVER_COLOR = (70, 90, 140)  # Button hover
BUTTON_ACTIVE_COLOR = (90, 110, 160)  # Active button
BUTTON_TEXT_COLOR = (255, 255, 255)  # Button text
BUTTON_DISABLED_COLOR = (30, 35, 50)  # Disabled button
BUTTON_DISABLED_TEXT = (120, 125, 135)  # Disabled text

# Tab constants
TAB_OVERVIEW = "Ship Overview"
TAB_CARGO = "Cargo Hold"
TAB_OUTFITS = "Outfits"
TAB_MISSIONS = "Missions"
TAB_CREW = "Crew"
TAB_SYSTEMS = "Systems"
TAB_FACTIONS = "Factions"

class ShipMenu:
    """Class to handle the player's ship management interface."""

    def __init__(self, game):
        """
        Initialize the modern ship menu.

        Args:
            game: The main game object
        """
        self.game = game
        self.current_tab = TAB_OVERVIEW
        self.tabs = [TAB_OVERVIEW, TAB_CARGO, TAB_OUTFITS, TAB_MISSIONS, TAB_CREW, TAB_SYSTEMS, TAB_FACTIONS]

        # UI elements
        self.tab_rects = {}
        self.back_button_rect = None
        self.abort_mission_button_rect = None

        # Scrolling for lists
        self.scroll_offset = 0
        self.max_visible_items = 12
        self.scroll_up_rect = None
        self.scroll_down_rect = None

        # Ship display
        self.ship_rotation = 0
        self.ship_rotation_speed = 1.0
        self.ship_sprite = None
        self.ship_display_size = (200, 200)

        # Animation
        self.animation_time = 0
        self.pulse_alpha = 0

    def show_ship_menu(self, screen):
        """
        Display the modern ship menu.

        Args:
            screen: The pygame screen to draw on

        Returns:
            str: The next state to transition to
        """
        # Set up UI elements and load ship sprite
        self._setup_ui(screen)
        self._load_ship_sprite()

        # Main menu loop
        running = True
        while running and self.game.running:
            # Update animations
            self.animation_time += 1
            self.ship_rotation = (self.ship_rotation + self.ship_rotation_speed) % 360
            self.pulse_alpha = int(127 + 127 * math.sin(self.animation_time * 0.05))

            # Clear screen with gradient background
            self._draw_background(screen)

            # Draw modern header
            self._draw_header(screen)

            # Draw tabs
            self._draw_modern_tabs(screen)

            # Draw content based on current tab
            if self.current_tab == TAB_OVERVIEW:
                self._draw_modern_overview_tab(screen)
            elif self.current_tab == TAB_CARGO:
                self._draw_modern_cargo_tab(screen)
            elif self.current_tab == TAB_OUTFITS:
                self._draw_modern_outfits_tab(screen)
            elif self.current_tab == TAB_MISSIONS:
                self._draw_modern_missions_tab(screen)
            elif self.current_tab == TAB_CREW:
                self._draw_modern_crew_tab(screen)
            elif self.current_tab == TAB_SYSTEMS:
                self._draw_modern_systems_tab(screen)
            elif self.current_tab == TAB_FACTIONS:
                self._draw_modern_factions_tab(screen)

            # Draw modern back button
            mouse_pos = pg.mouse.get_pos()
            button_color = BUTTON_HOVER_COLOR if self.back_button_rect.collidepoint(mouse_pos) else BUTTON_COLOR
            pg.draw.rect(screen, button_color, self.back_button_rect)
            pg.draw.rect(screen, PANEL_BORDER, self.back_button_rect, 2)
            self.game.draw_text("Back", 18, BUTTON_TEXT_COLOR,
                               self.back_button_rect.centerx, self.back_button_rect.centery, align="center")

            # Handle events
            for event in pg.event.get():
                if event.type == pg.QUIT:
                    self.game.running = False
                    running = False

                elif event.type == pg.KEYDOWN:
                    if event.key == pg.K_ESCAPE or event.key == pg.K_p:
                        running = False

                elif event.type == pg.MOUSEBUTTONDOWN:
                    mouse_pos = pg.mouse.get_pos()

                    # Check if a tab was clicked
                    for tab, rect in self.tab_rects.items():
                        if rect.collidepoint(mouse_pos):
                            self.current_tab = tab
                            self.scroll_offset = 0  # Reset scroll when changing tabs
                            break

                    # Check if back button was clicked
                    if self.back_button_rect.collidepoint(mouse_pos):
                        running = False

                    # Check abort mission button (only in missions tab)
                    if (self.current_tab == TAB_MISSIONS and
                        self.abort_mission_button_rect.collidepoint(mouse_pos) and
                        self.game.mission_system.active_missions):
                        self._abort_current_mission()

                    # Check scroll buttons if visible
                    if self.scroll_up_rect and self.scroll_up_rect.collidepoint(mouse_pos):
                        self.scroll_offset = max(0, self.scroll_offset - 1)
                    elif self.scroll_down_rect and self.scroll_down_rect.collidepoint(mouse_pos):
                        # Max offset depends on the current tab
                        if self.current_tab == TAB_CARGO:
                            max_items = len(self.game.player.cargo)
                        elif self.current_tab == TAB_OUTFITS:
                            max_items = len(self.game.player.outfits)
                        else:
                            max_items = 0

                        max_offset = max(0, max_items - self.max_visible_items)
                        self.scroll_offset = min(max_offset, self.scroll_offset + 1)

            pg.display.flip()
            self.game.clock.tick(60)  # Use a default FPS of 60

        return "PLAYING"

    def _load_ship_sprite(self):
        """Load the ship sprite for display."""
        try:
            # Try to load the ship's spritesheet
            self.ship_sprite = load_sprite("ship", self.game.player.ship.name.lower().replace(" ", "_"),
                                         self.game.player.ship.size)
            if not self.ship_sprite or not self.ship_sprite.image:
                # Fallback to ship's static image
                if hasattr(self.game.player.ship, 'image') and self.game.player.ship.image:
                    self.ship_sprite = self.game.player.ship.image
                else:
                    self.ship_sprite = None
        except Exception as e:
            print(f"Failed to load ship sprite: {e}")
            self.ship_sprite = None

    def _setup_ui(self, screen):
        """Set up UI elements for the modern ship menu."""
        width, height = screen.get_width(), screen.get_height()

        # Tab buttons - modern style with spacing
        tab_width = (width - 100) / len(self.tabs)
        tab_start_x = 50
        for i, tab in enumerate(self.tabs):
            x = tab_start_x + i * tab_width
            self.tab_rects[tab] = pg.Rect(x, 80, tab_width - 10, 45)

        # Back button - modern style
        self.back_button_rect = pg.Rect(width - 120, height - 60, 100, 40)

        # Abort mission button (for missions tab)
        self.abort_mission_button_rect = pg.Rect(width - 250, height - 60, 120, 40)

        # Scroll buttons
        button_size = 35
        self.scroll_up_rect = pg.Rect(width - 60, 150, button_size, button_size)
        self.scroll_down_rect = pg.Rect(width - 60, height - 120, button_size, button_size)

    def _draw_tabs(self, screen):
        """Draw the tab buttons at the top of the screen."""
        for tab, rect in self.tab_rects.items():
            color = HIGHLIGHT_COLOR if tab == self.current_tab else PANEL_COLOR
            pg.draw.rect(screen, color, rect)
            pg.draw.rect(screen, TEXT_COLOR, rect, 2)  # Border

            self.game.draw_text(tab, 24, TEXT_COLOR,
                               rect.centerx, rect.centery, align="center")

    def _draw_overview_tab(self, screen):
        """Draw the overview tab content."""
        width, height = screen.get_width(), screen.get_height()

        # Draw ship image/model
        ship_panel = pg.Rect(50, 100, width * 0.4, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, ship_panel)
        pg.draw.rect(screen, TEXT_COLOR, ship_panel, 2)

        # Draw rotated ship image
        if self.game.player.ship.image:
            # Create a copy of the original image for rotation
            ship_image = pg.transform.scale(self.game.player.ship.image,
                                           (int(ship_panel.width * 0.7), int(ship_panel.height * 0.7)))

            # Apply rotation for 3D effect
            rotated_image = pg.transform.rotate(ship_image, self.ship_rotation)

            # Get the rect of the rotated image and center it
            rotated_rect = rotated_image.get_rect(center=ship_panel.center)

            # Draw the rotated image
            screen.blit(rotated_image, rotated_rect)
        else:
            # Draw placeholder if no image
            self.game.draw_text("Ship Model", 36, TEXT_COLOR,
                               ship_panel.centerx, ship_panel.centery, align="center")

        # Draw ship name and class
        self.game.draw_text(f"{self.game.player.ship_name}", 36, TITLE_COLOR,
                           width * 0.75, 100, align="center")
        self.game.draw_text(f"Class: {self.game.player.ship.ship_class.capitalize()}", 24, TEXT_COLOR,
                           width * 0.75, 140, align="center")
        self.game.draw_text(f"Size: {self.game.player.ship.size.capitalize()}", 24, TEXT_COLOR,
                           width * 0.75, 170, align="center")

        # Draw ship stats
        stats_panel = pg.Rect(width * 0.5, 200, width * 0.45, height - 300)
        pg.draw.rect(screen, PANEL_COLOR, stats_panel)
        pg.draw.rect(screen, TEXT_COLOR, stats_panel, 2)

        # Stats title
        self.game.draw_text("Ship Specifications", 28, TITLE_COLOR,
                           stats_panel.centerx, stats_panel.top + 30, align="center")

        # Draw stats in two columns
        left_col_x = stats_panel.left + 30
        right_col_x = stats_panel.centerx + 30
        start_y = stats_panel.top + 80
        line_height = 30

        # Left column stats
        self.game.draw_text(f"Hull: {self.game.player.armor}/{self.game.player.max_armor}", 20, STAT_GOOD_COLOR,
                           left_col_x, start_y, align="topleft")
        self.game.draw_text(f"Shields: {int(self.game.player.shields)}/{self.game.player.max_shields}", 20, STAT_GOOD_COLOR,
                           left_col_x, start_y + line_height, align="topleft")
        self.game.draw_text(f"Cargo Space: {sum(self.game.player.cargo.values())}/{self.game.player.cargo_space} tons", 20, STAT_NEUTRAL_COLOR,
                           left_col_x, start_y + 2*line_height, align="topleft")
        self.game.draw_text(f"Outfit Space: {self.game.player.used_outfit_space}/{self.game.player.outfit_space} tons", 20, STAT_NEUTRAL_COLOR,
                           left_col_x, start_y + 3*line_height, align="topleft")
        self.game.draw_text(f"Credits: {self.game.player.credits}", 20, STAT_NEUTRAL_COLOR,
                           left_col_x, start_y + 4*line_height, align="topleft")

        # Right column stats
        self.game.draw_text(f"Speed: {self.game.player.max_speed:.1f}", 20, STAT_GOOD_COLOR,
                           right_col_x, start_y, align="topleft")
        self.game.draw_text(f"Acceleration: {self.game.player.acceleration:.1f}", 20, STAT_GOOD_COLOR,
                           right_col_x, start_y + line_height, align="topleft")
        self.game.draw_text(f"Turn Rate: {self.game.player.turn_rate:.1f}", 20, STAT_GOOD_COLOR,
                           right_col_x, start_y + 2*line_height, align="topleft")

        # Draw ship description
        desc_panel = pg.Rect(50, height - 90, width - 200, 70)
        pg.draw.rect(screen, PANEL_COLOR, desc_panel)
        pg.draw.rect(screen, TEXT_COLOR, desc_panel, 2)

        self.game.draw_text(self.game.player.ship.description, 16, TEXT_COLOR,
                           desc_panel.left + 10, desc_panel.top + 10, align="topleft")

    def _draw_cargo_tab(self, screen):
        """Draw the cargo tab content."""
        width, height = screen.get_width(), screen.get_height()

        # Draw cargo panel
        cargo_panel = pg.Rect(50, 100, width - 100, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, cargo_panel)
        pg.draw.rect(screen, TEXT_COLOR, cargo_panel, 2)

        # Draw cargo title and summary
        self.game.draw_text("Cargo Manifest", 32, TITLE_COLOR,
                           cargo_panel.centerx, cargo_panel.top + 30, align="center")

        cargo_used = sum(self.game.player.cargo.values())
        self.game.draw_text(f"Cargo Space: {cargo_used}/{self.game.player.cargo_space} tons", 24, STAT_NEUTRAL_COLOR,
                           cargo_panel.centerx, cargo_panel.top + 70, align="center")

        # Draw scroll buttons if needed
        if len(self.game.player.cargo) > self.max_visible_items:
            pg.draw.rect(screen, BUTTON_COLOR, self.scroll_up_rect)
            pg.draw.rect(screen, BUTTON_COLOR, self.scroll_down_rect)
            self.game.draw_text("▲", 20, BUTTON_TEXT_COLOR,
                               self.scroll_up_rect.centerx, self.scroll_up_rect.centery, align="center")
            self.game.draw_text("▼", 20, BUTTON_TEXT_COLOR,
                               self.scroll_down_rect.centerx, self.scroll_down_rect.centery, align="center")

        # Draw cargo list
        if not self.game.player.cargo:
            self.game.draw_text("No cargo", 28, TEXT_COLOR,
                               cargo_panel.centerx, cargo_panel.centery, align="center")
            return

        # Draw column headers
        header_y = cargo_panel.top + 120
        self.game.draw_text("Commodity", 24, TITLE_COLOR,
                           cargo_panel.left + 50, header_y, align="topleft")
        self.game.draw_text("Quantity", 24, TITLE_COLOR,
                           cargo_panel.left + 350, header_y, align="topleft")
        self.game.draw_text("Value (est.)", 24, TITLE_COLOR,
                           cargo_panel.left + 500, header_y, align="topleft")

        # Draw horizontal line
        pg.draw.line(screen, TEXT_COLOR,
                    (cargo_panel.left + 20, header_y + 30),
                    (cargo_panel.right - 20, header_y + 30), 2)

        # Get visible cargo items
        cargo_items = list(self.game.player.cargo.items())
        visible_items = cargo_items[self.scroll_offset:self.scroll_offset + self.max_visible_items]

        # Draw cargo items
        for i, (commodity_id, quantity) in enumerate(visible_items):
            y = header_y + 50 + i * 40

            # Get commodity info
            from game_objects.commodities import COMMODITIES
            commodity = COMMODITIES.get(commodity_id)
            if not commodity:
                continue

            # Draw commodity name
            name_color = TEXT_COLOR
            if commodity.illegal:
                name_color = STAT_BAD_COLOR  # Red for illegal goods
            self.game.draw_text(commodity.name, 20, name_color,
                               cargo_panel.left + 50, y, align="topleft")

            # Draw quantity
            self.game.draw_text(f"{quantity} tons", 20, TEXT_COLOR,
                               cargo_panel.left + 350, y, align="topleft")

            # Draw estimated value (base price * quantity)
            est_value = commodity.base_price * quantity
            self.game.draw_text(f"{est_value} cr", 20, STAT_GOOD_COLOR,
                               cargo_panel.left + 500, y, align="topleft")

    def _draw_outfits_tab(self, screen):
        """Draw the outfits tab content."""
        width, height = screen.get_width(), screen.get_height()

        # Draw outfits panel
        outfits_panel = pg.Rect(50, 100, width - 100, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, outfits_panel)
        pg.draw.rect(screen, TEXT_COLOR, outfits_panel, 2)

        # Draw outfits title and summary
        self.game.draw_text("Installed Outfits", 32, TITLE_COLOR,
                           outfits_panel.centerx, outfits_panel.top + 30, align="center")

        self.game.draw_text(f"Outfit Space: {self.game.player.used_outfit_space}/{self.game.player.outfit_space} tons", 24, STAT_NEUTRAL_COLOR,
                           outfits_panel.centerx, outfits_panel.top + 70, align="center")

        # Draw scroll buttons if needed
        if len(self.game.player.outfits) > self.max_visible_items:
            pg.draw.rect(screen, BUTTON_COLOR, self.scroll_up_rect)
            pg.draw.rect(screen, BUTTON_COLOR, self.scroll_down_rect)
            self.game.draw_text("▲", 20, BUTTON_TEXT_COLOR,
                               self.scroll_up_rect.centerx, self.scroll_up_rect.centery, align="center")
            self.game.draw_text("▼", 20, BUTTON_TEXT_COLOR,
                               self.scroll_down_rect.centerx, self.scroll_down_rect.centery, align="center")

        # Draw outfits list
        if not self.game.player.outfits:
            self.game.draw_text("No outfits installed", 28, TEXT_COLOR,
                               outfits_panel.centerx, outfits_panel.centery, align="center")
            return

        # Draw column headers
        header_y = outfits_panel.top + 120
        self.game.draw_text("Outfit", 24, TITLE_COLOR,
                           outfits_panel.left + 50, header_y, align="topleft")
        self.game.draw_text("Quantity", 24, TITLE_COLOR,
                           outfits_panel.left + 350, header_y, align="topleft")
        self.game.draw_text("Space Used", 24, TITLE_COLOR,
                           outfits_panel.left + 500, header_y, align="topleft")

        # Draw horizontal line
        pg.draw.line(screen, TEXT_COLOR,
                    (outfits_panel.left + 20, header_y + 30),
                    (outfits_panel.right - 20, header_y + 30), 2)

        # Get visible outfit items
        outfit_items = list(self.game.player.outfits.items())
        visible_items = outfit_items[self.scroll_offset:self.scroll_offset + self.max_visible_items]

        # Draw outfit items
        for i, (outfit_id, quantity) in enumerate(visible_items):
            y = header_y + 50 + i * 40

            # Get outfit info
            from game_objects.outfits import get_outfit_by_id
            outfit = get_outfit_by_id(outfit_id)
            if not outfit:
                continue

            # Draw outfit name
            self.game.draw_text(outfit.name, 20, TEXT_COLOR,
                               outfits_panel.left + 50, y, align="topleft")

            # Draw quantity
            self.game.draw_text(f"{quantity}", 20, TEXT_COLOR,
                               outfits_panel.left + 350, y, align="topleft")

            # Draw space used
            space_used = outfit.space_required * quantity
            self.game.draw_text(f"{space_used} tons", 20, TEXT_COLOR,
                               outfits_panel.left + 500, y, align="topleft")

    def _draw_crew_tab(self, screen):
        """Draw the crew tab content (placeholder for future implementation)."""
        width, height = screen.get_width(), screen.get_height()

        # Draw crew panel
        crew_panel = pg.Rect(50, 100, width - 100, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, crew_panel)
        pg.draw.rect(screen, TEXT_COLOR, crew_panel, 2)

        # Draw placeholder text
        self.game.draw_text("Crew Management", 32, TITLE_COLOR,
                           crew_panel.centerx, crew_panel.top + 30, align="center")

        self.game.draw_text("This feature is not yet implemented", 28, TEXT_COLOR,
                           crew_panel.centerx, crew_panel.centery, align="center")

        self.game.draw_text("Future feature: Assign crew to different ship systems", 20, TEXT_COLOR,
                           crew_panel.centerx, crew_panel.centery + 50, align="center")
        self.game.draw_text("for bonuses to performance, combat, and special abilities", 20, TEXT_COLOR,
                           crew_panel.centerx, crew_panel.centery + 80, align="center")

    def _draw_systems_tab(self, screen):
        """Draw the systems tab content (placeholder for future implementation)."""
        width, height = screen.get_width(), screen.get_height()

        # Draw systems panel
        systems_panel = pg.Rect(50, 100, width - 100, height - 200)
        pg.draw.rect(screen, PANEL_COLOR, systems_panel)
        pg.draw.rect(screen, TEXT_COLOR, systems_panel, 2)

        # Draw placeholder text
        self.game.draw_text("Ship Systems", 32, TITLE_COLOR,
                           systems_panel.centerx, systems_panel.top + 30, align="center")

        self.game.draw_text("This feature is not yet implemented", 28, TEXT_COLOR,
                           systems_panel.centerx, systems_panel.centery, align="center")

        self.game.draw_text("Future feature: Adjust power allocation to different ship systems", 20, TEXT_COLOR,
                           systems_panel.centerx, systems_panel.centery + 50, align="center")
        self.game.draw_text("such as engines, weapons, shields, and special equipment", 20, TEXT_COLOR,
                           systems_panel.centerx, systems_panel.centery + 80, align="center")

    # ===== MODERN UI METHODS =====

    def _draw_background(self, screen):
        """Draw modern gradient background."""
        width, height = screen.get_width(), screen.get_height()

        # Create gradient effect
        for y in range(height):
            ratio = y / height
            r = int(BACKGROUND_COLOR[0] * (1 - ratio * 0.3))
            g = int(BACKGROUND_COLOR[1] * (1 - ratio * 0.3))
            b = int(BACKGROUND_COLOR[2] * (1 - ratio * 0.2))
            color = (max(0, r), max(0, g), max(0, b))
            pg.draw.line(screen, color, (0, y), (width, y))

    def _draw_header(self, screen):
        """Draw modern header with ship name and status."""
        width = screen.get_width()

        # Header background
        header_rect = pg.Rect(0, 0, width, 75)
        pg.draw.rect(screen, PANEL_COLOR, header_rect)
        pg.draw.rect(screen, PANEL_BORDER, header_rect, 2)

        # Ship name and class
        self.game.draw_text(f"Ship: {self.game.player.ship_name}", 32, TITLE_COLOR,
                           50, 25, align="topleft")
        self.game.draw_text(f"{self.game.player.ship.ship_class.title()} Class • {self.game.player.ship.size.title()}",
                           18, SUBTITLE_COLOR, 50, 50, align="topleft")

        # Credits and status
        self.game.draw_text(f"Credits: {self.game.player.credits:,}", 24, SECONDARY_ACCENT,
                           width - 50, 25, align="topright")

        # Ship status indicators
        hull_percent = (self.game.player.armor / self.game.player.max_armor) * 100
        shield_percent = (self.game.player.shields / self.game.player.max_shields) * 100

        hull_color = STAT_GOOD_COLOR if hull_percent > 75 else STAT_NEUTRAL_COLOR if hull_percent > 25 else STAT_CRITICAL_COLOR
        shield_color = STAT_GOOD_COLOR if shield_percent > 50 else STAT_NEUTRAL_COLOR if shield_percent > 25 else STAT_BAD_COLOR

        self.game.draw_text(f"Hull: {hull_percent:.0f}%", 18, hull_color,
                           width - 50, 45, align="topright")

    def _draw_modern_tabs(self, screen):
        """Draw modern tab buttons."""
        for tab, rect in self.tab_rects.items():
            # Tab styling
            is_active = tab == self.current_tab
            mouse_pos = pg.mouse.get_pos()
            is_hovered = rect.collidepoint(mouse_pos)

            if is_active:
                color = BUTTON_ACTIVE_COLOR
                border_color = ACCENT_COLOR
                text_color = TITLE_COLOR
            elif is_hovered:
                color = BUTTON_HOVER_COLOR
                border_color = PANEL_BORDER
                text_color = TEXT_COLOR
            else:
                color = BUTTON_COLOR
                border_color = PANEL_BORDER
                text_color = SUBTITLE_COLOR

            # Draw tab background with rounded corners effect
            pg.draw.rect(screen, color, rect)
            pg.draw.rect(screen, border_color, rect, 2)

            # Add glow effect for active tab
            if is_active:
                glow_rect = rect.inflate(4, 4)
                glow_color = (*ACCENT_COLOR[:3], self.pulse_alpha)
                pg.draw.rect(screen, ACCENT_COLOR, glow_rect, 1)

            # Draw tab text
            self.game.draw_text(tab, 18, text_color, rect.centerx, rect.centery, align="center")

    def _draw_modern_overview_tab(self, screen):
        """Draw modern overview tab with ship display and stats."""
        width, height = screen.get_width(), screen.get_height()

        # Ship display panel (left side)
        ship_panel = pg.Rect(50, 140, width * 0.45, height - 220)
        self._draw_modern_panel(screen, ship_panel, "Ship Display")

        # Draw ship sprite/model
        ship_display_rect = pg.Rect(ship_panel.x + 20, ship_panel.y + 60,
                                   ship_panel.width - 40, ship_panel.height - 100)

        if self.ship_sprite:
            self._draw_ship_display(screen, ship_display_rect)
        else:
            # Fallback display
            pg.draw.rect(screen, HIGHLIGHT_COLOR, ship_display_rect)
            self.game.draw_text("Ship Model", 24, TEXT_COLOR,
                               ship_display_rect.centerx, ship_display_rect.centery, align="center")

        # Stats panel (right side)
        stats_panel = pg.Rect(width * 0.52, 140, width * 0.43, height - 220)
        self._draw_modern_panel(screen, stats_panel, "Ship Specifications")

        # Draw detailed stats
        self._draw_ship_stats(screen, stats_panel)

    def _draw_modern_panel(self, screen, rect, title):
        """Draw a modern panel with title."""
        # Panel background
        pg.draw.rect(screen, PANEL_COLOR, rect)
        pg.draw.rect(screen, PANEL_BORDER, rect, 2)

        # Title bar
        title_rect = pg.Rect(rect.x, rect.y, rect.width, 40)
        pg.draw.rect(screen, HIGHLIGHT_COLOR, title_rect)
        pg.draw.rect(screen, PANEL_BORDER, title_rect, 2)

        # Title text
        self.game.draw_text(title, 20, TITLE_COLOR, title_rect.centerx, title_rect.centery, align="center")

    def _draw_ship_display(self, screen, rect):
        """Draw the ship sprite with rotation and effects."""
        if hasattr(self.ship_sprite, 'get_frame'):
            # Spritesheet - get frame based on rotation
            frame_count = len(self.ship_sprite.frames) if hasattr(self.ship_sprite, 'frames') else 36
            frame_index = int((self.ship_rotation / 360) * frame_count) % frame_count
            ship_image = self.ship_sprite.get_frame(frame_index)
        else:
            # Static image - apply rotation
            ship_image = self.ship_sprite
            if ship_image:
                ship_image = pg.transform.rotate(ship_image, self.ship_rotation)

        if ship_image:
            # Scale to fit display area
            display_size = min(rect.width - 40, rect.height - 40)
            ship_image = pg.transform.scale(ship_image, (display_size, display_size))

            # Center the image
            image_rect = ship_image.get_rect(center=rect.center)
            screen.blit(ship_image, image_rect)

            # Add glow effect
            glow_rect = image_rect.inflate(20, 20)
            pg.draw.rect(screen, (*ACCENT_COLOR, 50), glow_rect, 3)

    def _draw_ship_stats(self, screen, panel_rect):
        """Draw detailed ship statistics."""
        y_start = panel_rect.y + 60
        line_height = 25
        col1_x = panel_rect.x + 20
        col2_x = panel_rect.x + panel_rect.width // 2 + 10

        # Column 1 - Combat Stats
        self.game.draw_text("Combat", 18, ACCENT_COLOR, col1_x, y_start, align="topleft")
        y = y_start + 30

        hull_percent = (self.game.player.armor / self.game.player.max_armor) * 100
        hull_color = STAT_GOOD_COLOR if hull_percent > 75 else STAT_NEUTRAL_COLOR if hull_percent > 25 else STAT_CRITICAL_COLOR

        self.game.draw_text(f"Hull: {self.game.player.armor:.0f}/{self.game.player.max_armor:.0f}", 16, hull_color, col1_x, y, align="topleft")
        y += line_height

        shield_percent = (self.game.player.shields / self.game.player.max_shields) * 100
        shield_color = STAT_GOOD_COLOR if shield_percent > 50 else STAT_NEUTRAL_COLOR if shield_percent > 25 else STAT_BAD_COLOR

        self.game.draw_text(f"Shields: {self.game.player.shields:.0f}/{self.game.player.max_shields:.0f}", 16, shield_color, col1_x, y, align="topleft")
        y += line_height

        # Power and Fuel
        power_percent = (self.game.player.power / self.game.player.power_capacity) * 100
        power_color = STAT_GOOD_COLOR if power_percent > 50 else STAT_NEUTRAL_COLOR if power_percent > 25 else STAT_BAD_COLOR

        self.game.draw_text(f"Power: {self.game.player.power:.0f}/{self.game.player.power_capacity:.0f}", 16, power_color, col1_x, y, align="topleft")
        y += line_height

        fuel_percent = (self.game.player.fuel / self.game.player.fuel_capacity) * 100
        fuel_color = STAT_GOOD_COLOR if fuel_percent > 50 else STAT_NEUTRAL_COLOR if fuel_percent > 25 else STAT_BAD_COLOR

        self.game.draw_text(f"Fuel: {self.game.player.fuel:.0f}/{self.game.player.fuel_capacity:.0f}", 16, fuel_color, col1_x, y, align="topleft")

        # Column 2 - Performance Stats
        self.game.draw_text("Performance", 18, ACCENT_COLOR, col2_x, y_start, align="topleft")
        y = y_start + 30

        self.game.draw_text(f"Max Speed: {self.game.player.max_speed:.1f}", 16, TEXT_COLOR, col2_x, y, align="topleft")
        y += line_height
        self.game.draw_text(f"Acceleration: {self.game.player.acceleration:.1f}", 16, TEXT_COLOR, col2_x, y, align="topleft")
        y += line_height
        self.game.draw_text(f"Turn Rate: {self.game.player.turn_rate:.1f}", 16, TEXT_COLOR, col2_x, y, align="topleft")
        y += line_height

        # Cargo and Outfit Space
        cargo_used = sum(self.game.player.cargo.values())
        cargo_percent = (cargo_used / self.game.player.cargo_space) * 100
        cargo_color = STAT_BAD_COLOR if cargo_percent > 90 else STAT_NEUTRAL_COLOR if cargo_percent > 75 else STAT_GOOD_COLOR

        self.game.draw_text(f"Cargo: {cargo_used}/{self.game.player.cargo_space} tons", 16, cargo_color, col2_x, y, align="topleft")
        y += line_height

        outfit_percent = (self.game.player.used_outfit_space / self.game.player.outfit_space) * 100
        outfit_color = STAT_BAD_COLOR if outfit_percent > 90 else STAT_NEUTRAL_COLOR if outfit_percent > 75 else STAT_GOOD_COLOR

        self.game.draw_text(f"Outfits: {self.game.player.used_outfit_space}/{self.game.player.outfit_space} tons", 16, outfit_color, col2_x, y, align="topleft")

    def _draw_modern_missions_tab(self, screen):
        """Draw modern missions tab with active missions and abort functionality."""
        width, height = screen.get_width(), screen.get_height()

        # Main missions panel
        missions_panel = pg.Rect(50, 140, width - 100, height - 220)
        self._draw_modern_panel(screen, missions_panel, "Active Missions")

        # Get active missions
        active_missions = self.game.mission_system.active_missions

        if not active_missions:
            self.game.draw_text("No active missions", 24, TEXT_COLOR,
                               missions_panel.centerx, missions_panel.centery, align="center")
            self.game.draw_text("Visit a mission board on any planet with tech level 1+ to find missions", 18, SUBTITLE_COLOR,
                               missions_panel.centerx, missions_panel.centery + 40, align="center")
            return

        # Draw missions list
        y_start = missions_panel.y + 60
        line_height = 80

        for i, mission in enumerate(active_missions):
            y = y_start + i * line_height
            mission_rect = pg.Rect(missions_panel.x + 20, y, missions_panel.width - 40, line_height - 10)

            # Mission background
            pg.draw.rect(screen, HIGHLIGHT_COLOR, mission_rect)
            pg.draw.rect(screen, PANEL_BORDER, mission_rect, 2)

            # Mission title
            self.game.draw_text(mission.title, 20, TITLE_COLOR,
                               mission_rect.x + 15, mission_rect.y + 10, align="topleft")

            # Mission description
            desc_lines = self._wrap_text(mission.description, 80)
            for j, line in enumerate(desc_lines[:2]):  # Show max 2 lines
                self.game.draw_text(line, 14, TEXT_COLOR,
                                   mission_rect.x + 15, mission_rect.y + 35 + j * 16, align="topleft")

            # Mission reward
            self.game.draw_text(f"Reward: {mission.reward} credits", 16, SECONDARY_ACCENT,
                               mission_rect.right - 15, mission_rect.y + 10, align="topright")

            # Mission status
            if hasattr(mission, 'destination_system'):
                dest_system = self.game.galaxy_systems.get(mission.destination_system)
                if dest_system:
                    self.game.draw_text(f"Destination: {dest_system.name}", 14, ACCENT_COLOR,
                                       mission_rect.right - 15, mission_rect.y + 30, align="topright")

        # Draw abort mission button if there are active missions
        if active_missions:
            mouse_pos = pg.mouse.get_pos()
            button_color = BUTTON_HOVER_COLOR if self.abort_mission_button_rect.collidepoint(mouse_pos) else BUTTON_COLOR
            pg.draw.rect(screen, button_color, self.abort_mission_button_rect)
            pg.draw.rect(screen, PANEL_BORDER, self.abort_mission_button_rect, 2)
            self.game.draw_text("Abort Mission", 16, BUTTON_TEXT_COLOR,
                               self.abort_mission_button_rect.centerx, self.abort_mission_button_rect.centery, align="center")

    def _wrap_text(self, text, max_chars):
        """Wrap text to fit within character limit."""
        words = text.split()
        lines = []
        current_line = ""

        for word in words:
            if len(current_line + word) <= max_chars:
                current_line += word + " "
            else:
                if current_line:
                    lines.append(current_line.strip())
                current_line = word + " "

        if current_line:
            lines.append(current_line.strip())

        return lines

    def _draw_modern_cargo_tab(self, screen):
        """Draw modern cargo tab."""
        width, height = screen.get_width(), screen.get_height()

        # Cargo panel
        cargo_panel = pg.Rect(50, 140, width - 100, height - 220)
        self._draw_modern_panel(screen, cargo_panel, "Cargo Manifest")

        # Cargo summary
        cargo_used = sum(self.game.player.cargo.values())
        cargo_percent = (cargo_used / self.game.player.cargo_space) * 100
        cargo_color = STAT_BAD_COLOR if cargo_percent > 90 else STAT_NEUTRAL_COLOR if cargo_percent > 75 else STAT_GOOD_COLOR

        self.game.draw_text(f"Cargo Space: {cargo_used}/{self.game.player.cargo_space} tons ({cargo_percent:.1f}%)",
                           18, cargo_color, cargo_panel.centerx, cargo_panel.y + 50, align="center")

        if not self.game.player.cargo:
            self.game.draw_text("Cargo hold is empty", 24, TEXT_COLOR,
                               cargo_panel.centerx, cargo_panel.centery, align="center")
            return

        # Draw cargo items
        self._draw_cargo_list(screen, cargo_panel)

    def _draw_modern_outfits_tab(self, screen):
        """Draw modern outfits tab."""
        width, height = screen.get_width(), screen.get_height()

        # Outfits panel
        outfits_panel = pg.Rect(50, 140, width - 100, height - 220)
        self._draw_modern_panel(screen, outfits_panel, "Installed Outfits")

        # Outfit summary
        outfit_percent = (self.game.player.used_outfit_space / self.game.player.outfit_space) * 100
        outfit_color = STAT_BAD_COLOR if outfit_percent > 90 else STAT_NEUTRAL_COLOR if outfit_percent > 75 else STAT_GOOD_COLOR

        self.game.draw_text(f"Outfit Space: {self.game.player.used_outfit_space}/{self.game.player.outfit_space} tons ({outfit_percent:.1f}%)",
                           18, outfit_color, outfits_panel.centerx, outfits_panel.y + 50, align="center")

        if not self.game.player.outfits:
            self.game.draw_text("No outfits installed", 24, TEXT_COLOR,
                               outfits_panel.centerx, outfits_panel.centery, align="center")
            return

        # Draw outfit items
        self._draw_outfits_list(screen, outfits_panel)

    def _draw_modern_crew_tab(self, screen):
        """Draw modern crew tab (placeholder)."""
        width, height = screen.get_width(), screen.get_height()

        crew_panel = pg.Rect(50, 140, width - 100, height - 220)
        self._draw_modern_panel(screen, crew_panel, "Crew Management")

        self.game.draw_text("Crew system coming soon!", 24, ACCENT_COLOR,
                           crew_panel.centerx, crew_panel.centery - 40, align="center")
        self.game.draw_text("Manage crew assignments and specializations", 18, TEXT_COLOR,
                           crew_panel.centerx, crew_panel.centery, align="center")
        self.game.draw_text("Crew bonuses will affect ship performance", 18, TEXT_COLOR,
                           crew_panel.centerx, crew_panel.centery + 30, align="center")

    def _draw_modern_systems_tab(self, screen):
        """Draw modern systems tab (placeholder)."""
        width, height = screen.get_width(), screen.get_height()

        systems_panel = pg.Rect(50, 140, width - 100, height - 220)
        self._draw_modern_panel(screen, systems_panel, "Ship Systems")

        self.game.draw_text("Power management system coming soon!", 24, ACCENT_COLOR,
                           systems_panel.centerx, systems_panel.centery - 40, align="center")
        self.game.draw_text("Allocate power between engines, weapons, and shields", 18, TEXT_COLOR,
                           systems_panel.centerx, systems_panel.centery, align="center")
        self.game.draw_text("Optimize your ship's performance for different situations", 18, TEXT_COLOR,
                           systems_panel.centerx, systems_panel.centery + 30, align="center")
    
    def _draw_modern_factions_tab(self, screen):
        """Draw faction relations tab showing player reputation."""
        width, height = screen.get_width(), screen.get_height()

        factions_panel = pg.Rect(50, 140, width - 100, height - 220)
        self._draw_modern_panel(screen, factions_panel, "Faction Relations")

        # Draw explanation
        self.game.draw_text("Your standing with the major galactic factions", 18, SUBTITLE_COLOR,
                           factions_panel.centerx, factions_panel.y + 50, align="center")

        # Draw faction list
        y_start = factions_panel.y + 90
        line_height = 40

        for i, (faction_id, faction) in enumerate(self.game.factions.items()):
            y = y_start + i * line_height
            item_rect = pg.Rect(factions_panel.x + 20, y, factions_panel.width - 40, line_height - 5)

            # Alternate row colors
            if i % 2 == 0:
                pg.draw.rect(screen, HIGHLIGHT_COLOR, item_rect)

            # Get reputation
            rep = self.game.player_reputation.get(faction_id, 0)
            
            # Determine status and color
            if rep >= self.game.REP_FRIENDLY_THRESHOLD:
                status = "Friendly"
                status_color = STAT_GOOD_COLOR
            elif rep <= self.game.REP_HOSTILE_THRESHOLD:
                status = "Hostile"
                status_color = STAT_BAD_COLOR
            elif rep < -10:
                status = "Unfriendly"
                status_color = STAT_CRITICAL_COLOR
            elif rep > 10:
                status = "Amicable"
                status_color = ACCENT_COLOR
            else:
                status = "Neutral"
                status_color = TEXT_COLOR

            # Draw faction name
            self.game.draw_text(faction.name, 18, TEXT_COLOR,
                               item_rect.x + 15, item_rect.centery - 8, align="midleft")
            
            # Draw reputation value and status
            self.game.draw_text(f"{rep:+d} ({status})", 16, status_color,
                               item_rect.right - 15, item_rect.centery - 8, align="midright")
            
            # Draw reputation bar
            bar_rect = pg.Rect(item_rect.x + 15, item_rect.centery + 5, item_rect.width - 30, 8)
            pg.draw.rect(screen, (30, 30, 30), bar_rect)  # Background
            
            # Calculate bar fill
            bar_fill_width = min(bar_rect.width, max(0, int((rep + 100) / 200 * bar_rect.width)))
            if bar_fill_width > 0:
                fill_rect = pg.Rect(bar_rect.x, bar_rect.y, bar_fill_width, bar_rect.height)
                pg.draw.rect(screen, status_color, fill_rect)
            
            pg.draw.rect(screen, PANEL_BORDER, bar_rect, 1)  # Border

    def _draw_cargo_list(self, screen, panel_rect):
        """Draw the cargo list with modern styling."""
        y_start = panel_rect.y + 80
        line_height = 30

        # Get visible cargo items
        cargo_items = list(self.game.player.cargo.items())
        visible_items = cargo_items[self.scroll_offset:self.scroll_offset + self.max_visible_items]

        for i, (commodity_id, quantity) in enumerate(visible_items):
            y = y_start + i * line_height
            item_rect = pg.Rect(panel_rect.x + 20, y, panel_rect.width - 40, line_height - 5)

            # Alternate row colors
            if i % 2 == 0:
                pg.draw.rect(screen, HIGHLIGHT_COLOR, item_rect)

            # Check if it's a mission cargo
            is_mission_cargo = commodity_id.startswith("mission_")

            if is_mission_cargo:
                # Mission cargo - special styling
                self.game.draw_text(f"Mission Cargo", 16, SECONDARY_ACCENT,
                                   item_rect.x + 10, item_rect.centery, align="midleft")
                self.game.draw_text(f"{quantity} tons", 16, SECONDARY_ACCENT,
                                   item_rect.right - 10, item_rect.centery, align="midright")
            else:
                # Regular cargo
                try:
                    from game_objects.commodities import COMMODITIES
                    commodity = COMMODITIES.get(commodity_id)
                    if commodity:
                        name_color = STAT_BAD_COLOR if commodity.illegal else TEXT_COLOR
                        self.game.draw_text(commodity.name, 16, name_color,
                                           item_rect.x + 10, item_rect.centery, align="midleft")
                        self.game.draw_text(f"{quantity} tons", 16, TEXT_COLOR,
                                           item_rect.right - 10, item_rect.centery, align="midright")
                    else:
                        self.game.draw_text(commodity_id, 16, TEXT_COLOR,
                                           item_rect.x + 10, item_rect.centery, align="midleft")
                        self.game.draw_text(f"{quantity} tons", 16, TEXT_COLOR,
                                           item_rect.right - 10, item_rect.centery, align="midright")
                except ImportError:
                    self.game.draw_text(commodity_id, 16, TEXT_COLOR,
                                       item_rect.x + 10, item_rect.centery, align="midleft")
                    self.game.draw_text(f"{quantity} tons", 16, TEXT_COLOR,
                                       item_rect.right - 10, item_rect.centery, align="midright")

    def _draw_outfits_list(self, screen, panel_rect):
        """Draw the outfits list with modern styling."""
        y_start = panel_rect.y + 80
        line_height = 30

        # Get visible outfit items
        outfit_items = list(self.game.player.outfits.items())
        visible_items = outfit_items[self.scroll_offset:self.scroll_offset + self.max_visible_items]

        for i, (outfit_id, quantity) in enumerate(visible_items):
            y = y_start + i * line_height
            item_rect = pg.Rect(panel_rect.x + 20, y, panel_rect.width - 40, line_height - 5)

            # Alternate row colors
            if i % 2 == 0:
                pg.draw.rect(screen, HIGHLIGHT_COLOR, item_rect)

            try:
                from game_objects.outfits import get_outfit_by_id
                outfit = get_outfit_by_id(outfit_id)
                if outfit:
                    # Outfit name
                    self.game.draw_text(outfit.name, 16, TEXT_COLOR,
                                       item_rect.x + 10, item_rect.centery, align="midleft")

                    # Quantity and space
                    space_used = outfit.space_required * quantity
                    self.game.draw_text(f"{quantity}x ({space_used} tons)", 16, SUBTITLE_COLOR,
                                       item_rect.right - 10, item_rect.centery, align="midright")
                else:
                    self.game.draw_text(outfit_id, 16, TEXT_COLOR,
                                       item_rect.x + 10, item_rect.centery, align="midleft")
                    self.game.draw_text(f"{quantity}x", 16, SUBTITLE_COLOR,
                                       item_rect.right - 10, item_rect.centery, align="midright")
            except ImportError:
                self.game.draw_text(outfit_id, 16, TEXT_COLOR,
                                   item_rect.x + 10, item_rect.centery, align="midleft")
                self.game.draw_text(f"{quantity}x", 16, SUBTITLE_COLOR,
                                   item_rect.right - 10, item_rect.centery, align="midright")

    def _abort_current_mission(self):
        """Abort the first active mission and remove its cargo."""
        if not self.game.mission_system.active_missions:
            return

        # Get the first active mission
        mission = self.game.mission_system.active_missions[0]

        # Remove mission cargo if it exists
        if hasattr(mission, 'mission_id'):
            cargo_key = f"mission_{mission.mission_id}"
            if cargo_key in self.game.player.cargo:
                del self.game.player.cargo[cargo_key]

        # Remove mission from active list
        self.game.mission_system.active_missions.remove(mission)

        # Show confirmation message
        self.game.set_status_message(f"Mission aborted: {mission.title}", (255, 150, 150), 180)
