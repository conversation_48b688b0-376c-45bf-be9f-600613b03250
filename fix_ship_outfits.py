#!/usr/bin/env python3
"""
Quick fix to add basic weapons to ships so AI can fight
"""
import json

def fix_ship_outfits():
    """Add basic default outfits to ships"""
    
    # Load current ships
    with open('ships_data.json', 'r') as f:
        ships_data = json.load(f)
    
    # Define basic loadouts by ship class
    basic_loadouts = {
        # Small ships - basic laser
        "scout": {"laser_cannon": 1},
        "light_fighter": {"laser_cannon": 1, "basicshieldbooster": 1},
        "kraken": {"laser_cannon": 1},
        "courier": {"laser_cannon": 1},
        
        # Medium ships - better weapons  
        "freighter": {"laser_cannon": 1},
        "heavyfighter": {"laser_cannon": 2, "basicshieldbooster": 1},
        "gunship": {"laser_cannon": 2, "misslerack": 1, "missle": 1},
        "passengerliner": {"laser_cannon": 1},
        
        # Large ships - more weapons
        "corvette": {"laser_cannon": 2, "Light Laser Turret": 1, "basicshieldbooster": 1},
        "bulkcarrier": {"laser_cannon": 1, "Light Laser Turret": 1},
        "frigate": {"laser_cannon": 2, "Light Laser Turret": 2, "basicshieldbooster": 1},
        "heavyfreighter": {"laser_cannon": 1, "Light Laser Turret": 1},
        
        # Capital ships - heavy weapons
        "battleship": {"laser_cannon": 4, "Heavy Beam Turret": 2, "basicshieldbooster": 2},
        "carrier": {"laser_cannon": 2, "Light Laser Turret": 4, "basicshieldbooster": 1},
        "cruiser": {"laser_cannon": 3, "Heavy Beam Turret": 1, "Light Laser Turret": 2, "basicshieldbooster": 1},
        "destroyer": {"laser_cannon": 4, "Heavy Beam Turret": 1, "misslerack": 2, "smartmissle": 2}
    }
    
    # Apply loadouts to ships
    updated_count = 0
    for ship_id, loadout in basic_loadouts.items():
        if ship_id in ships_data:
            ships_data[ship_id]["default_outfits"] = loadout
            updated_count += 1
            print(f"Updated {ship_id}: {loadout}")
    
    # Save updated ships
    with open('ships_data.json', 'w') as f:
        json.dump(ships_data, f, indent=2)
    
    print(f"\n✅ Updated {updated_count} ships with default outfits!")
    print("AI ships will now spawn with proper weapons.")

if __name__ == "__main__":
    fix_ship_outfits()
