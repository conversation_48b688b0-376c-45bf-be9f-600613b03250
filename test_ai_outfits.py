#!/usr/bin/env python3
"""
Quick test to verify AI ships now get proper weapons
"""
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

def test_ship_outfits():
    """Test if ships have proper default outfits"""
    from game_objects.ships import get_ship_by_id
    
    test_ships = ["scout", "light_fighter", "gunship"]
    
    for ship_id in test_ships:
        ship = get_ship_by_id(ship_id)
        if ship:
            outfits = getattr(ship, 'default_outfits', {})
            print(f"{ship_id}: {outfits}")
        else:
            print(f"Failed to load {ship_id}")

if __name__ == "__main__":
    test_ship_outfits()
