"""
Animation Endpoint UI Components
Provides reusable UI components for configuring animation endpoints in the editor.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import json
from pathlib import Path


class AnimationEndpointGrid:
    """UI component for editing animation endpoints."""
    
    def __init__(self, parent, title="Animation Endpoints"):
        self.parent = parent
        self.title = title
        self.endpoints = {}
        self.vars = {}
        
        # Create the main frame
        self.frame = ttk.LabelFrame(parent, text=title)
        
        # Available effects (will be populated dynamically)
        self.available_effects = self._get_available_effects()
        
        # Available position types
        self.position_types = [
            "impact_point", "ship_center", "ship_rear", "ship_front",
            "weapon_mount", "projectile_center", "projectile_rear", 
            "projectile_front", "entity_center"
        ]
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the UI components."""
        # Header
        header_frame = ttk.Frame(self.frame)
        header_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(header_frame, text="Configure visual effects triggered by game events", 
                 font=('Arial', 9, 'italic')).pack()
        
        # Refresh button
        ttk.Button(header_frame, text="Refresh Effects", 
                  command=self.refresh_effects).pack(side=tk.RIGHT)
        
        # Scrollable frame for endpoints - improved layout with flexible height
        canvas_frame = ttk.Frame(self.frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.canvas = tk.Canvas(canvas_frame, height=300)  # Increased default height
        self.scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        self.scrollable_frame.bind("<Configure>",
                                  lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all")))

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas for better scrolling
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def add_endpoint(self, endpoint_name, description=""):
        """Add an animation endpoint configuration."""
        if endpoint_name in self.endpoints:
            return
        
        # Create frame for this endpoint
        endpoint_frame = ttk.LabelFrame(self.scrollable_frame, text=f"{endpoint_name.replace('_', ' ').title()}")
        endpoint_frame.pack(fill=tk.X, padx=5, pady=5)
        
        if description:
            desc_label = ttk.Label(endpoint_frame, text=description, font=('Arial', 8, 'italic'))
            desc_label.pack(anchor=tk.W, padx=5)
        
        # Create grid for endpoint properties
        grid_frame = ttk.Frame(endpoint_frame)
        grid_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Configure grid columns
        for i in range(6):
            grid_frame.columnconfigure(i, weight=1)
        
        row = 0
        
        # Effect selection
        ttk.Label(grid_frame, text="Effect:").grid(row=row, column=0, sticky=tk.W, padx=2)
        effect_var = tk.StringVar()
        effect_combo = ttk.Combobox(grid_frame, textvariable=effect_var, 
                                   values=self.available_effects, width=15, state="readonly")
        effect_combo.grid(row=row, column=1, sticky=tk.W, padx=2)
        
        # Position type
        ttk.Label(grid_frame, text="Position:").grid(row=row, column=2, sticky=tk.W, padx=2)
        position_var = tk.StringVar(value="impact_point")
        position_combo = ttk.Combobox(grid_frame, textvariable=position_var,
                                     values=self.position_types, width=12, state="readonly")
        position_combo.grid(row=row, column=3, sticky=tk.W, padx=2)
        
        row += 1
        
        # Duration and Scale
        ttk.Label(grid_frame, text="Duration:").grid(row=row, column=0, sticky=tk.W, padx=2)
        duration_var = tk.DoubleVar(value=0.5)
        duration_spin = ttk.Spinbox(grid_frame, from_=0.1, to=5.0, increment=0.1, 
                                   textvariable=duration_var, width=8)
        duration_spin.grid(row=row, column=1, sticky=tk.W, padx=2)
        
        ttk.Label(grid_frame, text="Scale:").grid(row=row, column=2, sticky=tk.W, padx=2)
        scale_var = tk.DoubleVar(value=1.0)
        scale_spin = ttk.Spinbox(grid_frame, from_=0.1, to=3.0, increment=0.1,
                                textvariable=scale_var, width=8)
        scale_spin.grid(row=row, column=3, sticky=tk.W, padx=2)
        
        # Continuous checkbox
        continuous_var = tk.BooleanVar()
        continuous_check = ttk.Checkbutton(grid_frame, text="Continuous", 
                                          variable=continuous_var)
        continuous_check.grid(row=row, column=4, sticky=tk.W, padx=2)
        
        # Preview button
        preview_btn = ttk.Button(grid_frame, text="Preview", width=8,
                                command=lambda: self.preview_effect(effect_var.get()))
        preview_btn.grid(row=row, column=5, sticky=tk.W, padx=2)
        
        row += 1
        
        # Offset configuration
        ttk.Label(grid_frame, text="Offset X:").grid(row=row, column=0, sticky=tk.W, padx=2)
        offset_x_var = tk.IntVar(value=0)
        offset_x_spin = ttk.Spinbox(grid_frame, from_=-50, to=50, increment=1,
                                   textvariable=offset_x_var, width=8)
        offset_x_spin.grid(row=row, column=1, sticky=tk.W, padx=2)
        
        ttk.Label(grid_frame, text="Offset Y:").grid(row=row, column=2, sticky=tk.W, padx=2)
        offset_y_var = tk.IntVar(value=0)
        offset_y_spin = ttk.Spinbox(grid_frame, from_=-50, to=50, increment=1,
                                   textvariable=offset_y_var, width=8)
        offset_y_spin.grid(row=row, column=3, sticky=tk.W, padx=2)
        
        # Store variables
        self.vars[endpoint_name] = {
            'effect': effect_var,
            'position': position_var,
            'duration': duration_var,
            'scale': scale_var,
            'continuous': continuous_var,
            'offset_x': offset_x_var,
            'offset_y': offset_y_var
        }
        
        self.endpoints[endpoint_name] = endpoint_frame
    
    def load_endpoints(self, endpoints_data):
        """Load endpoint data from JSON into the UI."""
        if not endpoints_data:
            return
        
        for endpoint_name, data in endpoints_data.items():
            if endpoint_name not in self.vars:
                continue
            
            vars_dict = self.vars[endpoint_name]
            
            # Load values with proper type handling
            vars_dict['effect'].set(data.get('effect', ''))
            
            # CRITICAL FIX: Ensure position is always a string
            position_value = data.get('position', 'impact_point')
            if isinstance(position_value, list):
                position_value = position_value[0] if position_value else 'impact_point'
            vars_dict['position'].set(str(position_value))
            
            vars_dict['duration'].set(data.get('duration', 0.5))
            vars_dict['scale'].set(data.get('scale', 1.0))
            vars_dict['continuous'].set(data.get('continuous', False))
            
            # Load offset
            offset = data.get('offset', [0, 0])
            if isinstance(offset, list) and len(offset) >= 2:
                vars_dict['offset_x'].set(offset[0])
                vars_dict['offset_y'].set(offset[1])
    
    def save_endpoints(self):
        """Save endpoint data from UI to dictionary format."""
        endpoints_data = {}
        
        for endpoint_name, vars_dict in self.vars.items():
            # Only save if an effect is selected
            effect = vars_dict['effect'].get()
            if not effect:
                continue
            
            endpoint_data = {
                'effect': effect,
                'position': vars_dict['position'].get(),
                'duration': vars_dict['duration'].get(),
                'scale': vars_dict['scale'].get(),
                'continuous': vars_dict['continuous'].get()
            }
            
            # Add offset if non-zero
            offset_x = vars_dict['offset_x'].get()
            offset_y = vars_dict['offset_y'].get()
            if offset_x != 0 or offset_y != 0:
                endpoint_data['offset'] = [offset_x, offset_y]
            
            endpoints_data[endpoint_name] = endpoint_data
        
        return endpoints_data
    
    def _get_available_effects(self):
        """Get list of available effect spritesheets."""
        effects = []
        
        # Check effects directory
        effects_dir = Path("assets/images/sprites/effects")
        if effects_dir.exists():
            for file_path in effects_dir.iterdir():
                if file_path.name.endswith('_spritesheet.png'):
                    effect_name = file_path.name.replace('_spritesheet.png', '')
                    effects.append(effect_name)
        
        # Add some default effects that might be created
        default_effects = [
            "explosion", "small_explosion", "large_explosion",
            "laser_hit", "missile_hit", "shield_hit",
            "engine_trail", "muzzle_flash", "missile_launch",
            "warp_effect"
        ]
        
        for effect in default_effects:
            if effect not in effects:
                effects.append(effect)
        
        return sorted(effects)
    
    def refresh_effects(self):
        """Refresh the list of available effects."""
        self.available_effects = self._get_available_effects()
        
        # Update all comboboxes
        for vars_dict in self.vars.values():
            if 'effect' in vars_dict:
                effect_combo = vars_dict['effect']
                # Find the combobox widget and update its values
                # This is a bit hacky but works for our purposes
                for child in self.scrollable_frame.winfo_children():
                    for grandchild in child.winfo_children():
                        for widget in grandchild.winfo_children():
                            if isinstance(widget, ttk.Combobox) and widget['textvariable'] == str(effect_combo):
                                widget['values'] = self.available_effects
    
    def preview_effect(self, effect_name):
        """Preview an effect (placeholder for now)."""
        if not effect_name:
            messagebox.showwarning("Preview", "No effect selected")
            return
        
        # For now, just show info about the effect
        effects_dir = Path("assets/images/sprites/effects")
        effect_path = effects_dir / f"{effect_name}_spritesheet.png"
        
        if effect_path.exists():
            messagebox.showinfo("Effect Preview", f"Effect: {effect_name}\nLocation: {effect_path}\n\nPreview functionality coming soon!")
        else:
            messagebox.showwarning("Effect Preview", f"Effect '{effect_name}' not found at {effect_path}\n\nMake sure to run the effect creation script first.")
    
    def pack(self, **kwargs):
        """Pack the frame."""
        self.frame.pack(**kwargs)
    
    def pack_forget(self):
        """Hide the frame."""
        self.frame.pack_forget()


class WeaponAnimationEndpoints(AnimationEndpointGrid):
    """Animation endpoints specific to weapons."""
    
    def __init__(self, parent):
        super().__init__(parent, "Weapon Animation Endpoints")
        
        # Add weapon-specific endpoints
        self.add_endpoint("muzzle_flash", "Effect when weapon fires")
        self.add_endpoint("impact_effect", "Effect when projectile hits target")


class AmmunitionAnimationEndpoints(AnimationEndpointGrid):
    """Animation endpoints specific to ammunition."""
    
    def __init__(self, parent):
        super().__init__(parent, "Ammunition Animation Endpoints")
        
        # Add ammunition-specific endpoints
        self.add_endpoint("trail_effect", "Continuous trail while projectile flies")
        self.add_endpoint("impact_explosion", "Explosion when projectile hits target")
        self.add_endpoint("timeout_explosion", "Explosion when projectile runs out of fuel")


class ShipAnimationEndpoints(AnimationEndpointGrid):
    """Animation endpoints specific to ships."""
    
    def __init__(self, parent):
        super().__init__(parent, "Ship Animation Endpoints")
        
        # Add ship-specific endpoints
        self.add_endpoint("engine_trail", "Engine thrust effect (continuous)")
        self.add_endpoint("shield_hit", "Effect when shields absorb damage")
        self.add_endpoint("hull_damage_50", "Effect when armor drops to 50% (continuous smoke)")
        self.add_endpoint("hull_damage_25", "Effect when armor drops to 25% (continuous fire)")
        self.add_endpoint("destruction", "Explosion when ship is destroyed")
