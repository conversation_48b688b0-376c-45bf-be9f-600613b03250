#!/usr/bin/env python3
"""
Test script to verify animation endpoints were added correctly
"""

import json
import os

def test_animation_endpoints():
    print("=== TESTING ANIMATION ENDPOINTS ===")
    
    # Test outfits
    if os.path.exists("outfits_data.json"):
        with open("outfits_data.json", 'r') as f:
            outfits_data = json.load(f)
        
        test_items = ['laser_cannon', 'misslerack', 'missle', 'smartmissle', 'Light Laser Turret']
        weapons_with_endpoints = 0
        ammo_with_endpoints = 0
        referenced_effects = set()
        
        print("OUTFIT ENDPOINTS:")
        for item_id in test_items:
            if item_id in outfits_data:
                outfit = outfits_data[item_id]
                category = outfit.get('category', '')
                has_endpoints = 'animation_endpoints' in outfit
                
                print(f"  {item_id} ({category}): {'✓' if has_endpoints else '✗'} has animation endpoints")
                
                if has_endpoints:
                    for endpoint_name, endpoint_config in outfit['animation_endpoints'].items():
                        effect = endpoint_config.get('effect', 'unknown')
                        position = endpoint_config.get('position', [0, 0])
                        trigger = endpoint_config.get('trigger', 'unknown')
                        print(f"    {endpoint_name}: {effect} at {position} trigger={trigger}")
                        referenced_effects.add(effect)
                    
                    if category == 'weapons':
                        weapons_with_endpoints += 1
                    elif category == 'ammunition':
                        ammo_with_endpoints += 1
            else:
                print(f"  {item_id}: ✗ not found in JSON")
        
        print(f"\nOUTFITS SUMMARY: {weapons_with_endpoints} weapons, {ammo_with_endpoints} ammo with endpoints")
        print(f"Effects referenced: {sorted(referenced_effects)}")
    
    # Test ships
    if os.path.exists("ships_data.json"):
        with open("ships_data.json", 'r') as f:
            ships_data = json.load(f)
        
        ships_with_endpoints = 0
        ship_effects = set()
        
        print("\nSHIP ENDPOINTS:")
        test_ships = ['scout']  # Test the main player ship
        
        for ship_id in test_ships:
            if ship_id in ships_data:
                ship = ships_data[ship_id]
                has_endpoints = 'animation_endpoints' in ship
                
                print(f"  {ship_id}: {'✓' if has_endpoints else '✗'} has animation endpoints")
                
                if has_endpoints:
                    for endpoint_name, endpoint_config in ship['animation_endpoints'].items():
                        effect = endpoint_config.get('effect', 'unknown')
                        position = endpoint_config.get('position', [0, 0])
                        trigger = endpoint_config.get('trigger', 'unknown')
                        print(f"    {endpoint_name}: {effect} at {position} trigger={trigger}")
                        ship_effects.add(effect)
                    ships_with_endpoints += 1
            else:
                print(f"  {ship_id}: ✗ not found in JSON")
        
        print(f"\nSHIPS SUMMARY: {ships_with_endpoints} ships with endpoints")
        print(f"Ship effects referenced: {sorted(ship_effects)}")
        
        # Combine all effects
        all_effects = referenced_effects.union(ship_effects)
        print(f"\nALL EFFECTS REFERENCED: {sorted(all_effects)}")
    
    print("\n=== ENDPOINT TEST COMPLETE ===")

if __name__ == "__main__":
    test_animation_endpoints()
