#!/usr/bin/env python3
"""
Test script to verify the import fix for animation_integration functions
"""

import sys
import os

# Add src directory to path so we can import game modules
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

def test_animation_integration_imports():
    """Test that animation_integration imports work correctly."""
    print("Testing animation_integration imports...")
    
    try:
        # Test the functions that should exist
        from game_objects.animation_integration import (
            trigger_ship_engine_trail,
            trigger_ship_damage_effects,
            trigger_weapon_fire,
            initialize_animation_system
        )
        print("✓ All expected functions imported successfully!")
        
        # Test that the old, non-existent functions are no longer imported
        try:
            from game_objects.animation_integration import stop_ship_engine_trail
            print("✗ ERROR: stop_ship_engine_trail should not exist!")
            return False
        except ImportError:
            print("✓ stop_ship_engine_trail correctly does not exist")
        
        try:
            from game_objects.animation_integration import update_ship_damage_effects
            print("✗ ERROR: update_ship_damage_effects should not exist!")
            return False
        except ImportError:
            print("✓ update_ship_damage_effects correctly does not exist")
            
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_player_imports():
    """Test that player.py can be imported without errors."""
    print("\nTesting player.py imports...")
    
    try:
        # Import pygame first (required by player.py)
        import pygame as pg
        pg.init()
        
        # This should work now without import errors
        from game_objects.player import Player
        print("✓ Player class imported successfully!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error in player.py: {e}")
        return False
    except Exception as e:
        print(f"✗ Other error in player.py: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("Testing Import Fixes")
    print("=" * 50)
    
    test1_passed = test_animation_integration_imports()
    test2_passed = test_player_imports()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED! Import issues have been fixed.")
        print("You should now be able to run your game without import errors.")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    print("=" * 50)

if __name__ == "__main__":
    main()
