#!/usr/bin/env python3
"""
Test AI Combat Fixes
Test the AI combat improvements including weapon firing, threat calculation, and weapon selection.
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Mock game environment for testing
class MockGame:
    def __init__(self):
        self.all_sprites = MockGroup()
        self.ai_ships = []
        self.planets = []
        self.player = None
        self.camera = MockCamera()
        self.frame_count = 0
        self.faction_relations = {}
        self.player_faction_id = "player"

class MockGroup:
    def add(self, sprite):
        pass

class MockCamera:
    def __init__(self):
        self.width = 1920
        self.height = 1080

class MockPlayer:
    def __init__(self):
        self.pos = MockVector2(500, 500)
        self.faction_id = "player"
        self.weapons = []
        self.armor = 100
        self.max_armor = 100
        self.size = "small"
    
    def alive(self):
        return True

class MockVector2:
    def __init__(self, x, y):
        self.x = x
        self.y = y
    
    def distance_to(self, other):
        dx = self.x - other.x
        dy = self.y - other.y
        return (dx*dx + dy*dy)**0.5

def test_ai_weapon_loading():
    """Test that AI ships properly load weapons and ammo."""
    print("🔧 Testing AI Weapon Loading...")
    
    try:
        from game_objects.ai_core import AIShipCore
        from game_objects.simple_ai import SimpleAI
        
        # Create mock game and AI ship
        game = MockGame()
        ai_ship = AIShipCore(game, 100, 100, "pirates", "scout")
        
        print(f"✅ AI Ship created: {ai_ship.ship_type}")
        print(f"✅ Weapons equipped: {len(ai_ship.weapons)}")
        
        for i, weapon in enumerate(ai_ship.weapons):
            can_fire = weapon.can_fire() if hasattr(weapon, 'can_fire') else "unknown"
            ammo_info = f"{getattr(weapon, 'current_ammo', 'N/A')}/{getattr(weapon, 'max_ammo', 'N/A')}" if hasattr(weapon, 'uses_ammo') and weapon.uses_ammo else "N/A"
            print(f"  Weapon {i+1}: {weapon.name} - Can fire: {can_fire}, Ammo: {ammo_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI Weapon Loading test failed: {e}")
        return False

def test_smart_weapon_selection():
    """Test the smart weapon selection system."""
    print("🎯 Testing Smart Weapon Selection...")
    
    try:
        from game_objects.ai_core import AIShipCore
        from game_objects.simple_ai import SimpleAI
        
        # Create mock setup
        game = MockGame()
        game.player = MockPlayer()
        
        ai_ship = AIShipCore(game, 200, 200, "pirates", "scout")
        simple_ai = SimpleAI(ai_ship)
        
        # Set a target
        simple_ai.target = game.player
        
        # Test weapon selection
        simple_ai._select_best_weapon()
        
        if simple_ai.active_weapon:
            print(f"✅ Selected weapon: {simple_ai.active_weapon.name}")
            print(f"✅ Mount type: {getattr(simple_ai.active_weapon, 'mount_type', 'unknown')}")
            print(f"✅ Can fire: {simple_ai.active_weapon.can_fire() if hasattr(simple_ai.active_weapon, 'can_fire') else 'unknown'}")
        else:
            print("⚠️  No weapon selected")
        
        return True
        
    except Exception as e:
        print(f"❌ Smart Weapon Selection test failed: {e}")
        return False

def test_threat_calculation():
    """Test the improved threat calculation."""
    print("⚔️  Testing Threat Calculation...")
    
    try:
        from game_objects.ai_core import AIShipCore
        from game_objects.simple_ai import SimpleAI
        
        # Create mock setup
        game = MockGame()
        game.player = MockPlayer()
        
        ai_ship = AIShipCore(game, 300, 300, "pirates", "scout")
        simple_ai = SimpleAI(ai_ship)
        
        # Test power calculation
        my_power = simple_ai._get_ship_power(ai_ship)
        player_power = simple_ai._get_ship_power(game.player)
        
        print(f"✅ AI Ship Power: {my_power:.2f}")
        print(f"✅ Player Power: {player_power:.2f}")
        
        threat_ratio = my_power / max(player_power, 1)
        print(f"✅ Threat Ratio: {threat_ratio:.2f}")
        
        if threat_ratio > 0.8:
            print(f"✅ AI would attack (ratio > 0.8)")
        elif threat_ratio < 0.4:
            print(f"✅ AI would flee (ratio < 0.4)")
        else:
            print(f"✅ AI would continue current behavior")
        
        return True
        
    except Exception as e:
        print(f"❌ Threat Calculation test failed: {e}")
        return False

def test_weapon_firing():
    """Test the weapon firing mechanics."""
    print("🔫 Testing Weapon Firing...")
    
    try:
        from game_objects.ai_core import AIShipCore
        from game_objects.simple_ai import SimpleAI
        
        # Create mock setup
        game = MockGame()
        game.player = MockPlayer()
        
        ai_ship = AIShipCore(game, 400, 400, "pirates", "scout")
        simple_ai = SimpleAI(ai_ship)
        simple_ai.target = game.player
        simple_ai.state = "ATTACKING"
        
        # Update weapons
        dt = 1.0/60.0
        simple_ai._update_weapons(dt)
        
        # Test attacking behavior (which includes firing)
        old_cooldowns = []
        for weapon in ai_ship.weapons:
            old_cooldowns.append(weapon.cooldown_timer)
        
        simple_ai._attacking_behavior()
        
        # Check if any weapons attempted to fire
        fired_weapon = False
        for i, weapon in enumerate(ai_ship.weapons):
            if weapon.cooldown_timer != old_cooldowns[i]:
                fired_weapon = True
                print(f"✅ Weapon {weapon.name} fired (cooldown changed)")
        
        if not fired_weapon:
            print("⚠️  No weapons fired - checking weapon status...")
            for weapon in ai_ship.weapons:
                can_fire = weapon.can_fire() if hasattr(weapon, 'can_fire') else False
                print(f"  {weapon.name}: can_fire={can_fire}, cooldown={weapon.cooldown_timer}")
        
        return True
        
    except Exception as e:
        print(f"❌ Weapon Firing test failed: {e}")
        return False

def main():
    """Run all AI combat fix tests."""
    print("🤖 AI Combat Fixes Test Suite")
    print("=" * 50)
    
    tests = [
        test_ai_weapon_loading,
        test_smart_weapon_selection,
        test_threat_calculation,
        test_weapon_firing,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
        print("-" * 30)
    
    print()
    print("=" * 50)
    print(f"🎯 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("✅ All AI combat fixes working correctly!")
        return True
    else:
        print(f"❌ {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
