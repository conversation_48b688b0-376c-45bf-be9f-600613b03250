"""
Enhanced AI Ship - Refactored and improved AI system for Escape Velocity Py
This is the main AI ship class that coordinates all AI subsystems.
"""
import pygame as pg
import random
import math
from game_objects.ai_core import AIShipCore, AI_STATE_ATTACKING, AI_STATE_FLEEING, AI_STATE_DISABLED
from game_objects.ai_states import AIStateManager
from game_objects.ai_sensors import AISensorManager
from game_objects.ai_combat import AICombatManager

class AIShip(AIShipCore):
    """
    Enhanced AI Ship with modular subsystems.
    Inherits basic functionality from AIShipCore and adds advanced AI behavior.
    """

    def __init__(self, game, pos_x, pos_y, faction_id, ship_id="scout"):
        super().__init__(game, pos_x, pos_y, faction_id, ship_id)

        # Initialize Simple AI instead of complex subsystems
        from game_objects.simple_ai import SimpleAI
        self.simple_ai = SimpleAI(self)

        print(f"AI Ship created: {self.ship_type} ({self.faction_id}) with {len(self.weapons)} weapons")

    def update(self, dt=1/60):
        """Main update loop for the AI ship."""
        # Update timers
        self.state_timer -= 1
        if self.shield_recharge_timer > 0:
            self.shield_recharge_timer -= 1
        elif self.shields < self.max_shields:
            self.shields = min(self.max_shields, self.shields + 0.1)

        # Power regeneration (if ship has power system)
        if hasattr(self.ship, 'regenerate_power'):
            self.ship.regenerate_power(dt)

        # Update Simple AI
        if hasattr(self, 'simple_ai'):
            self.simple_ai.update(dt)

        # Update weapons
        for weapon in self.weapons:
            weapon.update(dt)

        # Update projectiles
        for projectile in list(self.projectiles):
            if projectile.update(dt):
                projectile.kill()

        # Apply movement and rotation
        self.apply_movement()
        self.rotate_image()

        # Update damage state effects
        self._update_damage_effects()

    def take_damage(self, amount, attacker_faction_id="unknown"):
        """
        Handle taking damage with reputation consequences.

        Args:
            amount: Damage amount
            attacker_faction_id: Faction ID of the attacker
        """
        # Apply damage to shields first, then health
        if self.shields > 0:
            self.shields -= amount
            if self.shields < 0:
                self.health += self.shields  # Overflow damage to health
                self.shields = 0
        else:
            self.health -= amount

        self.shield_recharge_timer = self.shield_recharge_delay
        
        # NEW: One-time reputation loss for attacking ships
        if attacker_faction_id == "player" and hasattr(self.game, 'player_reputation'):
            # Only apply penalty if this ship hasn't been attacked before
            ship_id = id(self)  # Use object ID as unique identifier
            if ship_id not in self.game.attacked_ships:
                self.game.attacked_ships.add(ship_id)
                rep_loss = self.game.SHIP_SIZE_REP_LOSS.get(self.ship.size, 1)
                old_rep = self.game.player_reputation.get(self.faction_id, 0)
                self.game.player_reputation[self.faction_id] = old_rep - rep_loss
                print(f"REPUTATION: Player first attacked {self.faction_id} {self.ship.size} ship (-{rep_loss}), new rep: {self.game.player_reputation[self.faction_id]}")

        # Handle death
        if self.health <= 0:
            # Additional reputation loss for destroying ships
            if attacker_faction_id == "player" and hasattr(self.game, 'player_reputation'):
                rep_loss = self.game.SHIP_SIZE_REP_LOSS.get(self.ship.size, 1)
                old_rep = self.game.player_reputation.get(self.faction_id, 0)
                self.game.player_reputation[self.faction_id] = old_rep - rep_loss
                print(f"REPUTATION: Player destroyed {self.faction_id} {self.ship.size} ship (-{rep_loss}), new rep: {self.game.player_reputation[self.faction_id]}")
                
            # Trigger destruction effects before killing
            self._trigger_destruction_effects()

            self.kill()
            print(f"{self.ship_type} ({self.faction_id}) destroyed!")
            self.create_explosion()
            self.drop_loot()
            return

        # Handle disable state - ship becomes disabled when shields are 0 and armor is at 15% or less
        disable_threshold = self.max_health * 0.15  # 15% armor threshold
        if self.shields <= 0 and self.health <= disable_threshold:
            print(f"DEBUG: {self.ship_type} ({self.faction_id}) disabled (shields: {self.shields:.1f}, armor: {self.health:.1f}/{self.max_health}).")
            self.game.set_status_message(f"{self.ship_type} disabled! Press B to board.", (255, 255, 0))
            # Remove ship from game (disabled ships disappear for now)
            self.kill()
            return

        # Combat response - immediate threat assessment when taking damage
        if hasattr(self, 'simple_ai'):
            print(f"{self.ship_type} took {amount} damage from {attacker_faction_id} - responding immediately!")
            # Force immediate threat scan and response
            self.simple_ai._respond_to_attack(attacker_faction_id)

    def _find_attacker(self, attacker_faction_id):
        """
        Find the entity that attacked us.

        Args:
            attacker_faction_id: Faction ID of the attacker

        Returns:
            Entity that attacked us, or None if not found
        """
        if attacker_faction_id == "player":
            return self.game.player
        else:
            # Find AI ship with matching faction
            for ship in self.game.ai_ships:
                if ship.faction_id == attacker_faction_id:
                    return ship
        return None

    def create_explosion(self):
        """Create an explosion effect when the ship is destroyed."""
        print(f"Explosion at {self.pos}")
        # TODO: Add visual explosion effect

    def drop_loot(self):
        """Drop loot when the ship is destroyed."""
        # Calculate loot value based on ship size
        size_multipliers = {"small": 1, "medium": 2, "large": 3, "capital": 5}
        base_loot_value = 100 * size_multipliers.get(self.ship.size, 1)
        loot_value = int(base_loot_value * random.uniform(0.5, 1.5))

        if hasattr(self.game, 'player') and self.game.player:
            self.game.player.credits += loot_value
            self.game.set_status_message(f"Collected {loot_value} credits from destroyed ship", (0, 255, 0))

        print(f"Dropped {loot_value} credits")

    def get_combat_effectiveness(self):
        """
        Calculate the combat effectiveness of this ship.

        Returns:
            float: Combat effectiveness score
        """
        if not self.weapons:
            return 0

        total_dps = sum(weapon.damage * weapon.fire_rate for weapon in self.weapons)
        health_factor = (self.health + self.shields) / (self.max_health + self.max_shields)

        return total_dps * health_factor

    def get_weapon_loadout_description(self):
        """
        Get a description of the ship's weapon loadout.

        Returns:
            str: Description of weapons
        """
        if not self.weapons:
            return "Unarmed"

        weapon_types = []
        if self.has_fixed_weapons:
            weapon_types.append("Fixed")
        if self.has_turret_weapons:
            weapon_types.append("Turret")
        if self.has_missile_weapons:
            weapon_types.append("Missile")

        return f"{len(self.weapons)} weapons ({', '.join(weapon_types)})"

    def debug_info(self):
        """
        Get debug information about the AI ship.

        Returns:
            dict: Debug information
        """
        simple_ai_state = "N/A"
        simple_ai_behavior = "N/A"
        if hasattr(self, 'simple_ai'):
            simple_ai_state = self.simple_ai.state
            simple_ai_behavior = self.simple_ai.behavior
            
        return {
            'ship_type': self.ship_type,
            'faction': self.faction_id,
            'ai_state': simple_ai_state,
            'ai_behavior': simple_ai_behavior,
            'health': f"{self.health:.1f}/{self.max_health}",
            'shields': f"{self.shields:.1f}/{self.max_shields}",
            'target': getattr(self.simple_ai, 'target', None) if hasattr(self, 'simple_ai') else 'None',
            'weapons': len(self.weapons),
            'weapon_loadout': self.get_weapon_loadout_description(),
            'position': f"({self.pos.x:.0f}, {self.pos.y:.0f})"
        }

    def _trigger_destruction_effects(self):
        """Trigger destruction animation effects."""
        # Get animation integration from the game instance
        if hasattr(self.game, 'animation_integration') and self.game.animation_integration:
            animation_integration = self.game.animation_integration

            # Trigger ship destruction effect
            animation_integration.trigger_ship_destruction(self)

            # Clean up all effects for this entity
            animation_integration.cleanup_entity_effects(self)

    def _update_damage_effects(self):
        """Update damage state effects for the AI ship."""
        # Get animation integration from the game instance
        if hasattr(self.game, 'animation_integration') and self.game.animation_integration:
            animation_integration = self.game.animation_integration

            # Set ship properties needed for animation lookup
            self.ship_id = self.ship_type
            self.id = self.ship_type

            # Update ship damage state effects
            animation_integration.update_ship_damage_state(self)

# Export the constants and classes that other modules need
__all__ = [
    'AIShip',
    'AI_STATE_IDLE', 'AI_STATE_PATROLLING', 'AI_STATE_ATTACKING',
    'AI_STATE_FLEEING', 'AI_STATE_TRADING', 'AI_STATE_DISABLED'
]
