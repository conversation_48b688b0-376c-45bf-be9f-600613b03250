#!/usr/bin/env python3
"""
Final verification script to check all animation endpoints
"""

import json
import os

def verify_animation_setup():
    print("=== FINAL ANIMATION ENDPOINTS VERIFICATION ===")
    
    # Check outfits
    if os.path.exists("outfits_data.json"):
        with open("outfits_data.json", 'r') as f:
            outfits_data = json.load(f)
        
        weapons_with_endpoints = 0
        ammo_with_endpoints = 0
        referenced_effects = set()
        
        print("OUTFIT ENDPOINTS:")
        for outfit_id, outfit_data in outfits_data.items():
            category = outfit_data.get('category', '')
            if 'animation_endpoints' in outfit_data:
                endpoints = outfit_data['animation_endpoints']
                
                for endpoint_name, endpoint_config in endpoints.items():
                    effect = endpoint_config.get('effect', 'unknown')
                    position = endpoint_config.get('position', [0, 0])
                    trigger = endpoint_config.get('trigger', 'unknown')
                    print(f"  {outfit_id} ({category}): {endpoint_name} -> {effect} at {position}")
                    referenced_effects.add(effect)
                
                if category == 'weapons':
                    weapons_with_endpoints += 1
                elif category == 'ammunition':
                    ammo_with_endpoints += 1
        
        print(f"\nOUTFITS SUMMARY: {weapons_with_endpoints} weapons, {ammo_with_endpoints} ammo with endpoints")
    
    # Check ships
    if os.path.exists("ships_data.json"):
        with open("ships_data.json", 'r') as f:
            ships_data = json.load(f)
        
        ships_with_endpoints = 0
        ship_effects = set()
        
        print("\nSHIP ENDPOINTS:")
        for ship_id, ship_data in ships_data.items():
            if 'animation_endpoints' in ship_data:
                endpoints = ship_data['animation_endpoints']
                
                for endpoint_name, endpoint_config in endpoints.items():
                    effect = endpoint_config.get('effect', 'unknown')
                    position = endpoint_config.get('position', [0, 0])
                    size = ship_data.get('size', 'unknown')
                    print(f"  {ship_id} ({size}): {endpoint_name} -> {effect} at {position}")
                    ship_effects.add(effect)
                
                ships_with_endpoints += 1
        
        print(f"\nSHIPS SUMMARY: {ships_with_endpoints} ships with endpoints")
        
        # Combine all effects
        all_effects = referenced_effects.union(ship_effects)
        print(f"\nALL EFFECTS REFERENCED: {sorted(all_effects)}")
    
    # Test specific items that are loaded by the player
    print("\n=== TESTING PLAYER DEFAULT ITEMS ===")
    test_items = ['laser_cannon', 'misslerack', 'missle', 'smartmissle', 'Light Laser Turret']
    
    for item_id in test_items:
        if item_id in outfits_data:
            has_endpoints = 'animation_endpoints' in outfits_data[item_id]
            category = outfits_data[item_id].get('category', '')
            print(f"  {item_id} ({category}): {'✓' if has_endpoints else '✗'} has animation endpoints")
        else:
            print(f"  {item_id}: ✗ not found in JSON")
    
    # Test player ship
    if 'scout' in ships_data:
        has_endpoints = 'animation_endpoints' in ships_data['scout']
        print(f"  scout (player ship): {'✓' if has_endpoints else '✗'} has animation endpoints")
    
    print("\n✅ ANIMATION SYSTEM SETUP COMPLETE!")
    print("\nYour game should now show:")
    print("- ✓ Muzzle flashes when weapons fire")
    print("- ✓ Engine trails when ships thrust")
    print("- ✓ Explosion effects when projectiles hit")
    print("\nRun the game and press:")
    print("- W key to see engine trails")
    print("- SPACE to see muzzle flashes")
    print("- Fire missiles to see impact explosions")

if __name__ == "__main__":
    verify_animation_setup()
