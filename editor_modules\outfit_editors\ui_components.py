"""
Standardized UI Components for Outfit Editors
Eliminates code duplication across outfit editor classes
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox

class StandardParameterGrid:
    """Reusable parameter grid for common outfit properties with improved layout support."""

    def __init__(self, parent, title="Basic Properties", columns=2):
        self.frame = ttk.LabelFrame(parent, text=title)
        self.vars = {}
        self.row = 0
        self.column = 0
        self.max_columns = columns
        self.column_width = 2  # Each field takes 2 columns (label + widget)

    def _advance_position(self):
        """Advance to next position in grid."""
        self.column += 1
        if self.column >= self.max_columns:
            self.column = 0
            self.row += 1

    def new_row(self):
        """Force start of a new row."""
        if self.column > 0:
            self.column = 0
            self.row += 1

    def add_string_field(self, label, var_name, width=25, **kwargs):
        """Add a string entry field."""
        col_offset = self.column * self.column_width
        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=col_offset, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.StringVar()
        ttk.Entry(self.frame, textvariable=self.vars[var_name], width=width, **kwargs).grid(
            row=self.row, column=col_offset + 1, padx=5, pady=2)
        self._advance_position()
        
    def add_int_field(self, label, var_name, min_val=0, max_val=100000, **kwargs):
        """Add an integer spinbox field."""
        col_offset = self.column * self.column_width
        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=col_offset, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.IntVar()
        ttk.Spinbox(self.frame, from_=min_val, to=max_val, textvariable=self.vars[var_name],
                   width=10, **kwargs).grid(row=self.row, column=col_offset + 1, padx=5, pady=2)
        self._advance_position()

    def add_float_field(self, label, var_name, min_val=0.0, max_val=100.0, increment=0.1, **kwargs):
        """Add a float spinbox field."""
        col_offset = self.column * self.column_width
        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=col_offset, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.DoubleVar()
        ttk.Spinbox(self.frame, from_=min_val, to=max_val, increment=increment,
                   textvariable=self.vars[var_name], width=10, **kwargs).grid(
                   row=self.row, column=col_offset + 1, padx=5, pady=2)
        self._advance_position()

    def add_combo_field(self, label, var_name, values, **kwargs):
        """Add a combobox field."""
        col_offset = self.column * self.column_width
        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=col_offset, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.StringVar()
        ttk.Combobox(self.frame, textvariable=self.vars[var_name], values=values,
                    width=15, **kwargs).grid(row=self.row, column=col_offset + 1, padx=5, pady=2)
        self._advance_position()
        
    def add_file_field(self, label, var_name, file_types=None, **kwargs):
        """Add a file browse field with visual feedback - spans full width."""
        if file_types is None:
            file_types = [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]

        # Force new row for file fields (they need full width)
        self.new_row()

        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=0, sticky=tk.W, padx=5, pady=2)
        self.vars[var_name] = tk.StringVar()

        # Trace changes to update file status
        self.vars[var_name].trace('w', lambda *args, vn=var_name: self._update_file_status(vn))

        entry_frame = ttk.Frame(self.frame)
        entry_frame.grid(row=self.row, column=1, columnspan=self.max_columns * self.column_width - 1,
                        padx=5, pady=2, sticky=tk.EW)

        # Entry field
        entry = ttk.Entry(entry_frame, textvariable=self.vars[var_name], width=30)
        entry.pack(side=tk.LEFT)

        # Browse button
        ttk.Button(entry_frame, text="Browse",
                  command=lambda: self._browse_file(self.vars[var_name], file_types)).pack(side=tk.LEFT, padx=2)

        # Clear button
        ttk.Button(entry_frame, text="Clear",
                  command=lambda: self.vars[var_name].set('')).pack(side=tk.LEFT, padx=2)

        self.row += 1

        # File status label
        status_label = ttk.Label(self.frame, text="No file selected", foreground="gray", font=('', 8))
        status_label.grid(row=self.row, column=1, columnspan=self.max_columns * self.column_width - 1,
                         sticky=tk.W, padx=5, pady=(0, 5))
        self.vars[f"{var_name}_status"] = status_label

        self.row += 1
        self.column = 0  # Reset to start of row

    def add_multi_select_field(self, label, var_name, options_callback, **kwargs):
        """Add a multi-select field for launcher compatibility - spans full width."""
        # Force new row for multi-select fields (they need full width)
        self.new_row()

        ttk.Label(self.frame, text=f"{label}:").grid(row=self.row, column=0, sticky=tk.W, padx=5, pady=2)

        # Create frame for listbox and scrollbar
        listbox_frame = ttk.Frame(self.frame)
        listbox_frame.grid(row=self.row, column=1, columnspan=self.max_columns * self.column_width - 1,
                          padx=5, pady=2, sticky=tk.EW)

        # Create listbox with scrollbar
        self.vars[var_name] = tk.Listbox(listbox_frame, selectmode=tk.MULTIPLE, height=4, width=25)
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL)
        self.vars[var_name].config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.vars[var_name].yview)

        self.vars[var_name].pack(side=tk.LEFT)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Store callback to refresh options
        self.vars[f"{var_name}_callback"] = options_callback

        # Add selection change handler to update status (NO AUTO-SAVE)
        self.vars[var_name].bind('<<ListboxSelect>>',
                                lambda e, vn=var_name: self._on_multiselect_change_no_autosave(vn))

        self.row += 1

        # Add status label to show selected items
        status_label = ttk.Label(self.frame, text="No items selected", foreground="gray", font=('', 8))
        status_label.grid(row=self.row, column=1, columnspan=self.max_columns * self.column_width - 1,
                         sticky=tk.W, padx=5, pady=(0, 5))
        self.vars[f"{var_name}_status"] = status_label

        self.row += 1
        self.column = 0  # Reset to start of row
        
    def _browse_file(self, var, file_types):
        """Browse for a file."""
        filename = filedialog.askopenfilename(filetypes=file_types)
        if filename:
            var.set(filename)
            
    def _update_file_status(self, var_name):
        """Update file status display."""
        import os
        status_label = self.vars.get(f"{var_name}_status")
        if not status_label:
            return
            
        filepath = self.vars[var_name].get().strip()
        
        if not filepath:
            status_label.config(text="No file selected", foreground="gray")
        elif os.path.exists(filepath):
            filename = os.path.basename(filepath)
            if len(filename) > 40:
                filename = filename[:37] + "..."
            status_label.config(text=f"✓ {filename}", foreground="green")
        else:
            filename = os.path.basename(filepath) if filepath else "Unknown"
            if len(filename) > 40:
                filename = filename[:37] + "..."
            status_label.config(text=f"✗ {filename} (not found)", foreground="red")
            
    def pack(self, **kwargs):
        """Pack the frame."""
        self.frame.pack(**kwargs)
        return self
        
    def grid(self, **kwargs):
        """Grid the frame."""
        self.frame.grid(**kwargs)
        return self
        
    def _on_multiselect_change(self, var_name):
        """Handle multiselect change - update status and trigger auto-save."""
        self._update_multiselect_status(var_name)
        # Store callback for parent to handle auto-save
        if hasattr(self, '_multiselect_change_callback'):
            self._multiselect_change_callback(var_name)
            
    def _on_multiselect_change_no_autosave(self, var_name):
        """Handle multiselect change - update status only (no auto-save)."""
        self._update_multiselect_status(var_name)
            
    def set_multiselect_change_callback(self, callback):
        """Set callback for when multiselect values change."""
        self._multiselect_change_callback = callback
            
    def _update_multiselect_status(self, var_name):
        """Update status label for multi-select field."""
        status_label = self.vars.get(f"{var_name}_status")
        listbox = self.vars.get(var_name)
        
        if not status_label or not listbox:
            return
            
        # Force update the selection display
        listbox.update_idletasks()
        
        selected_indices = listbox.curselection()
        if selected_indices:
            selected_items = [listbox.get(i) for i in selected_indices]
            if len(selected_items) == 1:
                status_text = f"✓ Selected: {selected_items[0]}"
                status_color = "green"
            else:
                status_text = f"✓ {len(selected_items)} items selected: {', '.join(selected_items[:2])}{'...' if len(selected_items) > 2 else ''}"
                status_color = "green"
        else:
            status_text = "No items selected"
            status_color = "gray"
            
        status_label.config(text=status_text, foreground=status_color)
        
        # Force visual refresh of the listbox
        listbox.update()
        
        # Ensure the parent window refreshes
        try:
            listbox.master.update_idletasks()
        except:
            pass

class ParameterLoader:
    """Handles loading and saving parameters with validation."""
    
    @staticmethod
    def load_outfit_parameters(outfit, parameter_grid):
        """Load outfit data into parameter grid variables."""
        for var_name, var in parameter_grid.vars.items():
            if var_name.endswith('_callback') or var_name.endswith('_status'):  # Skip callback functions and status labels
                continue
                
            value = getattr(outfit, var_name, None)
            if value is not None:
                if isinstance(var, tk.Listbox):  # Multi-select field
                    ParameterLoader._load_multi_select(var, value, parameter_grid.vars.get(f"{var_name}_callback"))
                elif isinstance(var, (tk.IntVar, tk.DoubleVar)):
                    var.set(value)
                else:
                    var.set(str(value))
                    
    @staticmethod
    def save_outfit_parameters(outfit, parameter_grid):
        """Save parameter grid variables to outfit object."""
        for var_name, var in parameter_grid.vars.items():
            if var_name.endswith('_callback') or var_name.endswith('_status'):  # Skip callback functions and status labels
                continue
                
            if isinstance(var, tk.Listbox):  # Multi-select field
                selected_values = ParameterLoader._get_multi_select_values(var)
                setattr(outfit, var_name, selected_values)
            elif hasattr(outfit, var_name):
                setattr(outfit, var_name, var.get())
            else:
                # Add new attribute if it doesn't exist
                setattr(outfit, var_name, var.get())
                
    @staticmethod
    def _load_multi_select(listbox, values, options_callback):
        """Load values into a multi-select listbox with improved visual feedback."""
        if options_callback:
            options = options_callback()
            listbox.delete(0, tk.END)
            
            # Populate listbox
            for option in options:
                listbox.insert(tk.END, option)
            
            # Handle values to select
            values_to_select = []
            if isinstance(values, list):
                values_to_select = values
            elif isinstance(values, str) and values.strip():
                values_to_select = [values]
            elif values:
                values_to_select = [str(values)]
            
            # Clear any existing selections first
            listbox.selection_clear(0, tk.END)
            
            if values_to_select:
                # Select matching items with enhanced visual feedback
                selections_made = 0
                for i, option in enumerate(options):
                    if option in values_to_select:
                        listbox.selection_set(i)
                        listbox.activate(i)
                        selections_made += 1
                
                # Force multiple visual updates to ensure display
                listbox.update_idletasks()
                listbox.update()
                
                # Scroll to show first selection
                selected_indices = listbox.curselection()
                if selected_indices:
                    listbox.see(selected_indices[0])
                    # Force another update after scrolling
                    listbox.update_idletasks()
                
                print(f"Multi-select: Made {selections_made} selections from {len(values_to_select)} values")
            
            # Ensure parent container refreshes
            try:
                listbox.master.update_idletasks()
                listbox.master.update()
            except:
                pass
                        
    @staticmethod
    def _get_multi_select_values(listbox):
        """Get selected values from a multi-select listbox."""
        selected_indices = listbox.curselection()
        return [listbox.get(i) for i in selected_indices]
                
class ProjectileParameterGrid(StandardParameterGrid):
    """Specialized grid for projectile parameters."""
    
    def __init__(self, parent):
        super().__init__(parent, "Projectile Properties")
        self.setup_projectile_fields()
        
    def setup_projectile_fields(self):
        """Setup projectile-specific fields."""
        self.add_int_field("Speed", "projectile_speed", 0, 1000)
        self.add_combo_field("Behavior", "projectile_behavior", 
                           ["instant", "beam", "dumbfire", "guided", "delayed", "proximity"])
        
        # Visual Properties for Lasers
        self.add_string_field("Beam Color (R,G,B)", "beam_color", 15)
        self.add_string_field("Beam Size (W,H)", "beam_size", 15)
        
        # Behavior Properties
        self.add_float_field("Tracking Strength", "tracking_strength", 0.0, 1.0, 0.1)
        self.add_int_field("Proximity Radius", "proximity_radius", 0, 100)
        self.add_float_field("Delay Time", "delay_time", 0.0, 10.0, 0.1)
