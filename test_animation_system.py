"""
Animation System Test Script
Run this to test if the animation endpoint system is working correctly.
"""

import json
import os
import sys

# Add the src directory to the path so we can import game modules
sys.path.insert(0, 'src')

def test_data_loading():
    """Test loading outfit and ship data."""
    print("Testing data loading...")
    
    # Test outfits data
    if os.path.exists("outfits_data.json"):
        with open("outfits_data.json", 'r') as f:
            outfits_data = json.load(f)
        
        # Count outfits with animation endpoints
        endpoint_count = 0
        for outfit_id, outfit_data in outfits_data.items():
            if 'animation_endpoints' in outfit_data:
                endpoint_count += 1
                print(f"  Found endpoints in {outfit_id}: {list(outfit_data['animation_endpoints'].keys())}")
        
        print(f"  Found {endpoint_count} outfits with animation endpoints")
    else:
        print("  ERROR: outfits_data.json not found!")
        return False
    
    return True


def test_effects_directory():
    """Test effects directory and files."""
    print("Testing effects directory...")
    
    effects_dir = "assets/images/sprites/effects"
    if not os.path.exists(effects_dir):
        print(f"  ERROR: Effects directory {effects_dir} not found!")
        return False
    
    # Check for effect files
    effect_files = []
    metadata_files = []
    
    for filename in os.listdir(effects_dir):
        if filename.endswith('_spritesheet.png'):
            effect_files.append(filename)
        elif filename.endswith('_spritesheet.json'):
            metadata_files.append(filename)
    
    print(f"  Found {len(effect_files)} effect spritesheets")
    print(f"  Found {len(metadata_files)} metadata files")
    
    # Test a few effect metadata files
    for metadata_file in metadata_files[:3]:
        metadata_path = os.path.join(effects_dir, metadata_file)
        try:
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            print(f"  ✓ {metadata_file}: {metadata['frame_count']} frames, {metadata['sprite_size']}px")
        except Exception as e:
            print(f"  ✗ {metadata_file}: Error loading - {e}")
    
    return len(metadata_files) > 0


def test_animation_integration():
    """Test animation integration module."""
    print("Testing animation integration...")
    
    try:
        from game_objects.animation_integration import initialize_animation_system
        print("  ✓ Animation integration module imported successfully")
        
        # Test initialization (without a real game object)
        class MockGame:
            def __init__(self):
                from game_objects.effects.effects_manager import EffectsManager
                self.effects_manager = EffectsManager(self)
        
        mock_game = MockGame()
        animation_manager = initialize_animation_system(mock_game)
        
        if animation_manager:
            print("  ✓ Animation manager initialized successfully")
            
            # Check if endpoints were loaded
            total_endpoints = 0
            for entity_type, entities in animation_manager.endpoints.items():
                for entity_id, endpoints in entities.items():
                    total_endpoints += len(endpoints)
            
            print(f"  ✓ Loaded {total_endpoints} animation endpoints")
            return True
        else:
            print("  ✗ Animation manager initialization failed")
            return False
            
    except Exception as e:
        print(f"  ✗ Animation integration test failed: {e}")
        return False


def test_sprite_loading():
    """Test sprite loading system."""
    print("Testing sprite loading...")
    
    try:
        from game_objects.sprite_manager import SpriteSheet
        
        # Test loading an effect spritesheet
        effects_dir = "assets/images/sprites/effects"
        for filename in os.listdir(effects_dir):
            if filename.endswith('_spritesheet.png'):
                sprite_path = os.path.join(effects_dir, filename)
                print(f"  Testing {sprite_path}...")
                
                spritesheet = SpriteSheet(sprite_path)
                if spritesheet and spritesheet.frames:
                    print(f"    ✓ Loaded {len(spritesheet.frames)} frames")
                    return True
                else:
                    print(f"    ✗ Failed to load frames")
                break
        else:
            print("  No effect spritesheets found to test")
            return False
            
    except Exception as e:
        print(f"  ✗ Sprite loading test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=== Animation System Test ===")
    print()
    
    tests = [
        ("Data Loading", test_data_loading),
        ("Effects Directory", test_effects_directory),
        ("Animation Integration", test_animation_integration),
        ("Sprite Loading", test_sprite_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  EXCEPTION: {e}")
            results.append((test_name, False))
        print()
    
    # Summary
    print("=== Test Results ===")
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("🎉 All tests passed! Animation system should be working.")
    else:
        print("⚠️  Some tests failed. Check the errors above.")


if __name__ == "__main__":
    main()
