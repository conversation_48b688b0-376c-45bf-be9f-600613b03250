"""
Defense Editor for the Enhanced Content Editor
Handles editing of defense outfits
"""

import tkinter as tk
from tkinter import ttk, messagebox
from .base_editor import BaseOutfitEditor
from .ui_components import StandardParameterGrid, ParameterLoader

class DefenseEditor(BaseOutfitEditor):
    """Editor for defense outfits."""
    
    def __init__(self, parent, data_manager):
        super().__init__(parent, data_manager, "defense")
    
    def setup_editor_ui(self, parent):
        """Setup the defense editor interface."""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties Grid - improved horizontal layout
        self.basic_grid = StandardParameterGrid(scrollable_frame, "Basic Properties", columns=3)
        self.basic_grid.add_string_field("Name", "name")
        self.basic_grid.add_int_field("Cost", "cost", 100, 100000)
        self.basic_grid.add_int_field("Space Required", "space_required", 1, 50)
        self.basic_grid.add_combo_field("Defense Type", "subcategory", ["shields", "armor", "point_defense", "ecm", "reactive"])
        self.basic_grid.add_int_field("Min Tech Level", "min_tech_level", 1, 10)
        self.basic_grid.add_file_field("Outfitter Icon", "outfitter_icon")
        self.basic_grid.add_file_field("Outfitter Image", "outfitter_image")
        self.basic_grid.pack(fill=tk.X, padx=5, pady=5)

        # Defense Type Selection
        type_frame = ttk.LabelFrame(scrollable_frame, text="Defense System Type")
        type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.defense_type_var = tk.StringVar(value="passive")
        ttk.Radiobutton(type_frame, text="Passive Defense (Shields/Armor)", 
                       variable=self.defense_type_var, value="passive",
                       command=self.on_defense_type_change).pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(type_frame, text="Active Defense (Point Defense/ECM)", 
                       variable=self.defense_type_var, value="active",
                       command=self.on_defense_type_change).pack(anchor=tk.W, padx=5, pady=2)
        ttk.Radiobutton(type_frame, text="Reactive Defense (Adaptive Systems)", 
                       variable=self.defense_type_var, value="reactive",
                       command=self.on_defense_type_change).pack(anchor=tk.W, padx=5, pady=2)

        # Passive Defense Properties Grid
        self.passive_grid = StandardParameterGrid(scrollable_frame, "Passive Defense Properties")
        self.passive_grid.add_int_field("Shield Boost", "shield_boost", 0, 2000)
        self.passive_grid.add_int_field("Armor Boost", "armor_boost", 0, 2000)
        self.passive_grid.add_float_field("Shield Recharge Boost", "shield_recharge_boost", 0.0, 50.0, 0.1)
        self.passive_grid.add_float_field("Damage Reduction", "damage_reduction", 0.0, 0.8, 0.01)
        self.passive_grid.add_float_field("Energy Drain", "energy_drain", 0.0, 50.0, 0.1)

        # Active Defense Properties Grid
        self.active_grid = StandardParameterGrid(scrollable_frame, "Active Defense Properties")
        self.active_grid.add_combo_field("Mount Type", "mount_type", ["passive", "fixed", "turret", "omni"])
        self.active_grid.add_int_field("Defense Range", "defense_range", 50, 1000)
        self.active_grid.add_float_field("Fire Rate", "fire_rate", 0.1, 20.0, 0.1)
        self.active_grid.add_float_field("Accuracy", "accuracy", 0.1, 1.0, 0.01)
        self.active_grid.add_float_field("Power Cost", "power_cost", 1.0, 100.0, 0.1)
        self.active_grid.add_int_field("Firing Arc", "firing_arc", 15, 360)
        
        # Electronic Warfare Properties Grid
        self.ecm_grid = StandardParameterGrid(scrollable_frame, "Electronic Warfare Properties")
        self.ecm_grid.add_float_field("Jam Strength", "jam_strength", 0.0, 1.0, 0.01)
        self.ecm_grid.add_float_field("Jam Resistance", "jam_resistance", 0.0, 1.0, 0.01)
        self.ecm_grid.add_int_field("Jam Range", "jam_range", 100, 2000)
        self.ecm_grid.add_float_field("Frequency Agility", "frequency_agility", 0.0, 1.0, 0.01)
        
        # Reactive Defense Properties Grid
        self.reactive_grid = StandardParameterGrid(scrollable_frame, "Reactive Defense Properties")
        self.reactive_grid.add_float_field("Activation Chance", "activation_chance", 0.0, 1.0, 0.01)
        self.reactive_grid.add_int_field("Activation Threshold", "activation_threshold", 1, 100)
        self.reactive_grid.add_float_field("Response Time", "response_time", 0.1, 5.0, 0.1)
        self.reactive_grid.add_float_field("Adaptation Rate", "adaptation_rate", 0.1, 2.0, 0.1)
        
        # Description
        desc_frame = ttk.LabelFrame(scrollable_frame, text="Description")
        desc_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.description_text = tk.Text(desc_frame, height=3, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=5)

        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        ttk.Button(save_frame, text="Save Defense", command=self.save_item).pack(side=tk.RIGHT, padx=5)
        
        # Initialize UI state
        self.on_defense_type_change()
    
    def on_defense_type_change(self):
        """Show/hide UI sections based on defense type."""
        defense_type = self.defense_type_var.get()
        
        # Hide all specialized grids first
        if hasattr(self, 'passive_grid'):
            self.passive_grid.frame.pack_forget()
        if hasattr(self, 'active_grid'):
            self.active_grid.frame.pack_forget()
        if hasattr(self, 'ecm_grid'):
            self.ecm_grid.frame.pack_forget()
        if hasattr(self, 'reactive_grid'):
            self.reactive_grid.frame.pack_forget()
            
        # Show relevant grids based on type
        if defense_type == "passive":
            if hasattr(self, 'passive_grid'):
                self.passive_grid.pack(fill=tk.X, padx=5, pady=5)
        elif defense_type == "active":
            if hasattr(self, 'active_grid'):
                self.active_grid.pack(fill=tk.X, padx=5, pady=5)
            if hasattr(self, 'ecm_grid'):
                self.ecm_grid.pack(fill=tk.X, padx=5, pady=5)
        elif defense_type == "reactive":
            if hasattr(self, 'reactive_grid'):
                self.reactive_grid.pack(fill=tk.X, padx=5, pady=5)
    
    def load_item_into_editor(self, defense):
        """Load defense data into the editor."""
        super().load_item_into_editor(defense)
        
        # Load using standardized parameter loader
        ParameterLoader.load_outfit_parameters(defense, self.basic_grid)
        ParameterLoader.load_outfit_parameters(defense, self.passive_grid)
        ParameterLoader.load_outfit_parameters(defense, self.active_grid)
        ParameterLoader.load_outfit_parameters(defense, self.ecm_grid)
        ParameterLoader.load_outfit_parameters(defense, self.reactive_grid)
        
        # Set defense type based on subcategory and properties
        subcategory = getattr(defense, 'subcategory', 'shields')
        if subcategory in ['shields', 'armor']:
            self.defense_type_var.set('passive')
        elif subcategory in ['point_defense', 'ecm']:
            self.defense_type_var.set('active')
        elif subcategory == 'reactive':
            self.defense_type_var.set('reactive')
        else:
            self.defense_type_var.set('passive')  # Default
            
        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(defense, 'description', ''))
        
        # Update UI visibility
        self.on_defense_type_change()
    
    def save_item(self):
        """Save the current defense with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No defense selected to save")
            return
        
        try:
            # Save using standardized parameter loader
            ParameterLoader.save_outfit_parameters(self.current_outfit, self.basic_grid)
            
            # Save type-specific parameters based on defense type
            defense_type = self.defense_type_var.get()
            if defense_type == "passive":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.passive_grid)
            elif defense_type == "active":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.active_grid)
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.ecm_grid)
            elif defense_type == "reactive":
                ParameterLoader.save_outfit_parameters(self.current_outfit, self.reactive_grid)
            
            # Save description
            self.current_outfit.description = self.description_text.get(1.0, tk.END).strip()
            
            super().save_item()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save defense: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new defense instance."""
        class SimpleDefense:
            def __init__(self, id, name):
                # Basic properties
                self.id = id
                self.name = name
                self.category = "defense"
                self.subcategory = "shields"
                self.cost = 5000
                self.space_required = 3
                self.min_tech_level = 1
                self.outfitter_icon = ""
                self.outfitter_image = ""
                
                # Passive Defense Properties
                self.shield_boost = 100
                self.armor_boost = 0
                self.shield_recharge_boost = 1.0
                self.damage_reduction = 0.0
                self.energy_drain = 2.0
                
                # Active Defense Properties
                self.mount_type = "passive"
                self.defense_range = 200
                self.fire_rate = 2.0
                self.accuracy = 0.7
                self.power_cost = 5.0
                self.firing_arc = 45
                
                # Electronic Warfare Properties
                self.jam_strength = 0.0
                self.jam_resistance = 0.0
                self.jam_range = 500
                self.frequency_agility = 0.0
                
                # Reactive Defense Properties
                self.activation_chance = 0.0
                self.activation_threshold = 10
                self.response_time = 1.0
                self.adaptation_rate = 1.0
                
                self.description = ""

        return SimpleDefense(item_id, item_id.replace('_', ' ').title())
