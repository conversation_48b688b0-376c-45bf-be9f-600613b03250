"""
Create Sample Effect Sprites for Animation Endpoint System

This script creates basic effect spritesheets for testing the animation endpoint system.
"""

import pygame as pg
import math
import os
import json


def create_explosion_effect(size=64, frames=8, color=(255, 100, 0)):
    """Create an explosion effect spritesheet."""
    spritesheet = pg.Surface((size * frames, size), pg.SRCALPHA)
    
    for frame in range(frames):
        # Calculate explosion properties
        progress = frame / (frames - 1)
        radius = int(size * 0.4 * (0.2 + 0.8 * progress))
        alpha = int(255 * (1.0 - progress * 0.8))
        
        # Create frame surface
        frame_surface = pg.Surface((size, size), pg.SRCALPHA)
        
        # Draw explosion circles
        for i in range(3):
            circle_radius = radius - i * 5
            if circle_radius > 0:
                circle_color = (
                    min(255, color[0] + i * 20),
                    max(0, color[1] - i * 20),
                    max(0, color[2] - i * 30),
                    max(50, alpha - i * 30)
                )
                pg.draw.circle(frame_surface, circle_color, (size//2, size//2), circle_radius)
        
        # Blit frame to spritesheet
        spritesheet.blit(frame_surface, (frame * size, 0))
    
    return spritesheet


def create_laser_hit_effect(size=32, frames=6, color=(255, 255, 100)):
    """Create a laser hit effect spritesheet."""
    spritesheet = pg.Surface((size * frames, size), pg.SRCALPHA)
    
    for frame in range(frames):
        progress = frame / (frames - 1)
        
        # Create frame surface
        frame_surface = pg.Surface((size, size), pg.SRCALPHA)
        
        # Draw sparks
        num_sparks = 8
        for i in range(num_sparks):
            angle = (i / num_sparks) * 2 * math.pi
            spark_length = int(size * 0.3 * (1.0 - progress))
            
            start_x = size // 2
            start_y = size // 2
            end_x = start_x + int(math.cos(angle) * spark_length)
            end_y = start_y + int(math.sin(angle) * spark_length)
            
            alpha = int(255 * (1.0 - progress))
            spark_color = (color[0], color[1], color[2], alpha)
            
            if spark_length > 0:
                pg.draw.line(frame_surface, spark_color, (start_x, start_y), (end_x, end_y), 2)
        
        # Central flash
        flash_radius = int(size * 0.15 * (1.0 - progress))
        if flash_radius > 0:
            alpha = int(255 * (1.0 - progress))
            flash_color = (255, 255, 255, alpha)
            pg.draw.circle(frame_surface, flash_color, (size//2, size//2), flash_radius)
        
        # Blit frame to spritesheet
        spritesheet.blit(frame_surface, (frame * size, 0))
    
    return spritesheet


def create_engine_trail_effect(size=32, frames=4, color=(0, 100, 255)):
    """Create an engine trail effect spritesheet."""
    spritesheet = pg.Surface((size * frames, size), pg.SRCALPHA)
    
    for frame in range(frames):
        progress = frame / frames
        
        # Create frame surface
        frame_surface = pg.Surface((size, size), pg.SRCALPHA)
        
        # Draw trail
        trail_length = int(size * 0.8)
        trail_width = int(size * 0.3)
        
        # Create gradient trail
        for i in range(trail_length):
            x = size // 2
            y = i
            width = int(trail_width * (1.0 - i / trail_length))
            
            # Animate the trail
            alpha = int(255 * (1.0 - i / trail_length) * (0.5 + 0.5 * math.sin(progress * 2 * math.pi)))
            trail_color = (color[0], color[1], color[2], max(50, alpha))
            
            if width > 0:
                pg.draw.circle(frame_surface, trail_color, (x, y), width // 2)
        
        # Blit frame to spritesheet
        spritesheet.blit(frame_surface, (frame * size, 0))
    
    return spritesheet


def create_shield_hit_effect(size=48, frames=5, color=(100, 100, 255)):
    """Create a shield hit effect spritesheet."""
    spritesheet = pg.Surface((size * frames, size), pg.SRCALPHA)
    
    for frame in range(frames):
        progress = frame / (frames - 1)
        
        # Create frame surface
        frame_surface = pg.Surface((size, size), pg.SRCALPHA)
        
        # Draw shield ripple
        ripple_radius = int(size * 0.4 * progress)
        ripple_thickness = max(1, int(size * 0.05 * (1.0 - progress)))
        alpha = int(255 * (1.0 - progress))
        
        if ripple_radius > 0:
            ripple_color = (color[0], color[1], color[2], alpha)
            pg.draw.circle(frame_surface, ripple_color, (size//2, size//2), ripple_radius, ripple_thickness)
        
        # Central flash
        flash_radius = int(size * 0.1 * (1.0 - progress))
        if flash_radius > 0:
            flash_alpha = int(255 * (1.0 - progress))
            flash_color = (255, 255, 255, flash_alpha)
            pg.draw.circle(frame_surface, flash_color, (size//2, size//2), flash_radius)
        
        # Blit frame to spritesheet
        spritesheet.blit(frame_surface, (frame * size, 0))
    
    return spritesheet


def create_muzzle_flash_effect(size=24, frames=3, color=(255, 255, 100)):
    """Create a muzzle flash effect spritesheet."""
    spritesheet = pg.Surface((size * frames, size), pg.SRCALPHA)
    
    for frame in range(frames):
        progress = frame / (frames - 1)
        
        # Create frame surface
        frame_surface = pg.Surface((size, size), pg.SRCALPHA)
        
        # Draw muzzle flash
        flash_radius = int(size * 0.4 * (1.0 - progress))
        alpha = int(255 * (1.0 - progress))
        
        if flash_radius > 0:
            flash_color = (color[0], color[1], color[2], alpha)
            pg.draw.circle(frame_surface, flash_color, (size//2, size//2), flash_radius)
        
        # Draw flash rays
        num_rays = 6
        for i in range(num_rays):
            angle = (i / num_rays) * 2 * math.pi
            ray_length = int(size * 0.3 * (1.0 - progress))
            
            start_x = size // 2
            start_y = size // 2
            end_x = start_x + int(math.cos(angle) * ray_length)
            end_y = start_y + int(math.sin(angle) * ray_length)
            
            if ray_length > 0:
                ray_color = (255, 255, 255, alpha)
                pg.draw.line(frame_surface, ray_color, (start_x, start_y), (end_x, end_y), 2)
        
        # Blit frame to spritesheet
        spritesheet.blit(frame_surface, (frame * size, 0))
    
    return spritesheet


def create_metadata(effect_name, size, frames):
    """Create metadata for an effect spritesheet."""
    return {
        "name": effect_name,
        "frame_width": size,
        "frame_height": size,
        "frame_count": frames,
        "animation_speed": 15.0,
        "loop": False
    }


def main():
    """Create all sample effect sprites."""
    pg.init()
    
    # Create effects directory if it doesn't exist
    effects_dir = "assets/images/sprites/effects"
    os.makedirs(effects_dir, exist_ok=True)
    
    # Define effects to create
    effects = [
        ("explosion", create_explosion_effect, 64, 8, (255, 100, 0)),
        ("small_explosion", create_explosion_effect, 48, 6, (255, 120, 20)),
        ("large_explosion", create_explosion_effect, 96, 12, (255, 80, 0)),
        ("laser_hit", create_laser_hit_effect, 32, 6, (255, 255, 100)),
        ("missile_hit", create_explosion_effect, 56, 8, (255, 150, 50)),
        ("engine_trail", create_engine_trail_effect, 32, 4, (0, 100, 255)),
        ("shield_hit", create_shield_hit_effect, 48, 5, (100, 100, 255)),
        ("muzzle_flash", create_muzzle_flash_effect, 24, 3, (255, 255, 100)),
        ("missile_launch", create_muzzle_flash_effect, 32, 4, (255, 200, 100)),
    ]
    
    print("Creating sample effect sprites...")
    
    for effect_name, create_func, size, frames, color in effects:
        print(f"Creating {effect_name}...")
        
        # Create the effect
        if create_func == create_explosion_effect:
            spritesheet = create_func(size, frames, color)
        elif create_func == create_laser_hit_effect:
            spritesheet = create_func(size, frames, color)
        elif create_func == create_engine_trail_effect:
            spritesheet = create_func(size, frames, color)
        elif create_func == create_shield_hit_effect:
            spritesheet = create_func(size, frames, color)
        elif create_func == create_muzzle_flash_effect:
            spritesheet = create_func(size, frames, color)
        
        # Save spritesheet
        spritesheet_path = os.path.join(effects_dir, f"{effect_name}_spritesheet.png")
        pg.image.save(spritesheet, spritesheet_path)
        
        # Save metadata
        metadata = create_metadata(effect_name, size, frames)
        metadata_path = os.path.join(effects_dir, f"{effect_name}_metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"  Saved {spritesheet_path}")
        print(f"  Saved {metadata_path}")
    
    print("Sample effect sprites created successfully!")
    pg.quit()


if __name__ == "__main__":
    main()
