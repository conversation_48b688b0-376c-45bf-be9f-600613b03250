"""
Animation Endpoint Manager

Central system that maps game events to visual effects through configurable "animation endpoints."
This system allows for data-driven visual effects without hardcoded effect names in game code.

Key Features:
- Register/trigger animation endpoints by name
- Handle effect positioning, timing, scaling
- Interface with existing EffectsManager
- Support continuous and one-shot animations
"""

import pygame as pg
from typing import Dict, List, Optional, Any, Tuple
from .position_calculator import PositionCalculator


class AnimationEndpoint:
    """Represents a single animation endpoint configuration."""
    
    def __init__(self, endpoint_name: str, config: Dict[str, Any]):
        """
        Initialize an animation endpoint.
        
        Args:
            endpoint_name: Name of the endpoint
            config: Configuration dictionary from JSON
        """
        self.name = endpoint_name
        self.effect = config.get('effect', 'explosion')
        self.position = config.get('position', 'entity_center')
        self.offset = config.get('offset', [0, 0])
        self.duration = config.get('duration', 1.0)
        self.scale = config.get('scale', 1.0)
        self.continuous = config.get('continuous', False)
        self.rotation = config.get('rotation', None)  # 'ship_angle', 'fixed', or None
        self.scale_with_damage = config.get('scale_with_damage', False)
        self.trigger_at_armor = config.get('trigger_at_armor', None)  # For damage state triggers
        
        # Runtime state for continuous effects
        self.active_effect = None
        self.is_active = False


class ContinuousEffect:
    """Tracks a continuous effect that needs regular updates."""
    
    def __init__(self, entity, endpoint: AnimationEndpoint, effect_instance):
        self.entity = entity
        self.endpoint = endpoint
        self.effect_instance = effect_instance
        self.last_position = None


class AnimationEndpointManager:
    """Central manager for animation endpoints."""
    
    def __init__(self, effects_manager):
        """
        Initialize the animation endpoint manager.
        
        Args:
            effects_manager: Reference to the game's EffectsManager
        """
        self.effects_manager = effects_manager
        
        # Registry of endpoints by entity type and ID
        self.endpoints: Dict[str, Dict[str, Dict[str, AnimationEndpoint]]] = {}
        
        # Active continuous effects
        self.continuous_effects: List[ContinuousEffect] = []
        
        # Entity damage state tracking
        self.entity_damage_states: Dict[Any, Dict[str, bool]] = {}
        
        print("AnimationEndpointManager initialized")
    
    def register_endpoint(self, entity_type: str, entity_id: str, endpoint_name: str, 
                         effect_config: Dict[str, Any]) -> None:
        """
        Register an animation endpoint for a specific entity type and ID.
        
        Args:
            entity_type: Type of entity ('weapon', 'ammo', 'ship')
            entity_id: Specific ID of the entity
            endpoint_name: Name of the endpoint
            effect_config: Configuration dictionary
        """
        if entity_type not in self.endpoints:
            self.endpoints[entity_type] = {}
        
        if entity_id not in self.endpoints[entity_type]:
            self.endpoints[entity_type][entity_id] = {}
        
        endpoint = AnimationEndpoint(endpoint_name, effect_config)
        self.endpoints[entity_type][entity_id][endpoint_name] = endpoint
        
        print(f"Registered endpoint: {entity_type}.{entity_id}.{endpoint_name}")
    
    def trigger_endpoint(self, entity, endpoint_name: str, position: Optional[Tuple[float, float]] = None, 
                        **kwargs) -> bool:
        """
        Trigger an animation endpoint for an entity.
        
        Args:
            entity: The game entity triggering the endpoint
            endpoint_name: Name of the endpoint to trigger
            position: Optional specific position (for impact_point effects)
            **kwargs: Additional parameters (damage_scale, etc.)
            
        Returns:
            True if endpoint was found and triggered, False otherwise
        """
        endpoint = self._find_endpoint(entity, endpoint_name)
        if not endpoint:
            return False
        
        # Calculate effect position
        # Ensure position_type is a string
        position_type = endpoint.position
        if isinstance(position_type, list):
            position_type = position_type[0] if position_type else 'entity_center'
        elif not isinstance(position_type, str):
            position_type = str(position_type)
            
        effect_position = PositionCalculator.calculate_position(
            entity, 
            position_type, 
            tuple(endpoint.offset),
            position
        )
        
        # Calculate effect scale
        effect_scale = endpoint.scale
        if endpoint.scale_with_damage and 'damage_scale' in kwargs:
            effect_scale *= kwargs['damage_scale']
        
        # Calculate effect rotation
        effect_rotation = 0
        if endpoint.rotation == 'ship_angle' and hasattr(entity, 'angle'):
            effect_rotation = entity.angle
        
        # Create the effect
        if endpoint.continuous:
            self._start_continuous_effect(entity, endpoint, effect_position, effect_scale, effect_rotation)
        else:
            self._create_one_shot_effect(endpoint, effect_position, effect_scale, effect_rotation)
        
        return True
    
    def update_continuous_effects(self, entity, dt: float) -> None:
        """
        Update continuous effects for an entity.
        
        Args:
            entity: The entity to update effects for
            dt: Delta time in seconds
        """
        # Update positions of continuous effects
        for effect in self.continuous_effects[:]:  # Copy list to allow removal during iteration
            if effect.entity == entity:
                # Recalculate position
                # Ensure position_type is a string
                position_type = effect.endpoint.position
                if isinstance(position_type, list):
                    position_type = position_type[0] if position_type else 'entity_center'
                elif not isinstance(position_type, str):
                    position_type = str(position_type)
                    
                new_position = PositionCalculator.calculate_position(
                    entity,
                    position_type,
                    tuple(effect.endpoint.offset)
                )
                
                # Update effect position if it has moved
                if effect.effect_instance and hasattr(effect.effect_instance, 'pos'):
                    effect.effect_instance.pos = new_position
                    effect.last_position = new_position
                
                # Check if effect is still alive
                if (effect.effect_instance and 
                    hasattr(effect.effect_instance, 'finished') and 
                    effect.effect_instance.finished):
                    self.continuous_effects.remove(effect)
    
    def cleanup_entity_effects(self, entity) -> None:
        """
        Clean up all effects associated with an entity.
        
        Args:
            entity: The entity to clean up effects for
        """
        # Remove continuous effects
        self.continuous_effects = [
            effect for effect in self.continuous_effects 
            if effect.entity != entity
        ]
        
        # Clean up damage state tracking
        if entity in self.entity_damage_states:
            del self.entity_damage_states[entity]
    
    def check_damage_state_triggers(self, entity) -> None:
        """
        Check if entity damage state has changed and trigger appropriate endpoints.
        
        Args:
            entity: The entity to check damage state for
        """
        if not hasattr(entity, 'armor') or not hasattr(entity, 'max_armor'):
            return
        
        armor_percent = entity.armor / entity.max_armor if entity.max_armor > 0 else 0
        
        # Initialize damage state tracking for this entity
        if entity not in self.entity_damage_states:
            self.entity_damage_states[entity] = {}
        
        # Check for damage state endpoints
        entity_endpoints = self._get_entity_endpoints(entity)
        if not entity_endpoints:
            return
        
        for endpoint_name, endpoint in entity_endpoints.items():
            if endpoint.trigger_at_armor is not None:
                state_key = f"{endpoint_name}_triggered"
                
                # Check if we should trigger this damage state
                if (armor_percent <= endpoint.trigger_at_armor and 
                    not self.entity_damage_states[entity].get(state_key, False)):
                    
                    # Trigger the endpoint
                    self.trigger_endpoint(entity, endpoint_name)
                    self.entity_damage_states[entity][state_key] = True
                
                # Reset state if armor is restored above threshold
                elif (armor_percent > endpoint.trigger_at_armor and 
                      self.entity_damage_states[entity].get(state_key, False)):
                    
                    # Stop continuous effect if it's running
                    if endpoint.continuous:
                        self._stop_continuous_effect(entity, endpoint)
                    
                    self.entity_damage_states[entity][state_key] = False

    def _find_endpoint(self, entity, endpoint_name: str) -> Optional[AnimationEndpoint]:
        """Find an endpoint for a given entity and endpoint name."""
        entity_endpoints = self._get_entity_endpoints(entity)
        return entity_endpoints.get(endpoint_name) if entity_endpoints else None

    def _get_entity_endpoints(self, entity) -> Optional[Dict[str, AnimationEndpoint]]:
        """Get all endpoints for a given entity."""
        # Try to determine entity type and ID
        entity_type, entity_id = self._get_entity_type_and_id(entity)

        if entity_type and entity_id:
            return self.endpoints.get(entity_type, {}).get(entity_id, {})

        return None

    def _get_entity_type_and_id(self, entity) -> Tuple[Optional[str], Optional[str]]:
        """Determine the entity type and ID for endpoint lookup."""
        # Check if entity has outfit information (weapons/ammo)
        if hasattr(entity, 'outfit_id'):
            # Determine if it's a weapon or ammo based on category
            if hasattr(entity, 'category'):
                if entity.category == 'ammunition':
                    return 'ammo', entity.outfit_id
                elif entity.category == 'weapons':
                    return 'weapon', entity.outfit_id

        # Check if it's a ship
        if hasattr(entity, 'ship_id'):
            return 'ship', entity.ship_id
        elif hasattr(entity, 'id') and hasattr(entity, 'ship_class'):
            return 'ship', entity.id

        # Check if it's a projectile with owner information
        if hasattr(entity, 'owner') and hasattr(entity, 'weapon_id'):
            return 'weapon', entity.weapon_id

        return None, None

    def _start_continuous_effect(self, entity, endpoint: AnimationEndpoint,
                                position: pg.math.Vector2, scale: float, rotation: float) -> None:
        """Start a continuous effect."""
        # Check if this continuous effect is already running
        for effect in self.continuous_effects:
            if effect.entity == entity and effect.endpoint.name == endpoint.name:
                return  # Already running

        # Create the effect
        effect_instance = self.effects_manager.create_effect(
            endpoint.effect,
            position,
            scale=scale,
            duration=endpoint.duration
        )

        if effect_instance:
            # Track as continuous effect
            continuous_effect = ContinuousEffect(entity, endpoint, effect_instance)
            self.continuous_effects.append(continuous_effect)
            endpoint.is_active = True

    def _create_one_shot_effect(self, endpoint: AnimationEndpoint,
                               position: pg.math.Vector2, scale: float, rotation: float) -> None:
        """Create a one-shot effect."""
        self.effects_manager.create_effect(
            endpoint.effect,
            position,
            scale=scale,
            duration=endpoint.duration
        )

    def _stop_continuous_effect(self, entity, endpoint: AnimationEndpoint) -> None:
        """Stop a continuous effect."""
        for effect in self.continuous_effects[:]:
            if effect.entity == entity and effect.endpoint.name == endpoint.name:
                # Mark effect as finished
                if effect.effect_instance and hasattr(effect.effect_instance, 'finished'):
                    effect.effect_instance.finished = True

                self.continuous_effects.remove(effect)
                endpoint.is_active = False
                break

    def load_endpoints_from_data(self, outfit_data: Dict[str, Any], ship_data: Dict[str, Any]) -> None:
        """
        Load animation endpoints from JSON data.

        Args:
            outfit_data: Loaded outfits_data.json
            ship_data: Loaded ships_data.json
        """
        # Load weapon and ammo endpoints from outfit data
        for outfit_id, outfit_config in outfit_data.items():
            if 'animation_endpoints' in outfit_config:
                category = outfit_config.get('category', 'unknown')
                entity_type = 'weapon' if category == 'weapons' else 'ammo' if category == 'ammunition' else 'outfit'

                for endpoint_name, endpoint_config in outfit_config['animation_endpoints'].items():
                    self.register_endpoint(entity_type, outfit_id, endpoint_name, endpoint_config)

        # Load ship endpoints from ship data
        for ship_id, ship_config in ship_data.items():
            if 'animation_endpoints' in ship_config:
                for endpoint_name, endpoint_config in ship_config['animation_endpoints'].items():
                    self.register_endpoint('ship', ship_id, endpoint_name, endpoint_config)

        print(f"Loaded animation endpoints from data files")

    def get_available_effects(self) -> List[str]:
        """Get list of available effects from the effects manager."""
        if hasattr(self.effects_manager, 'effect_definitions'):
            return list(self.effects_manager.effect_definitions.keys())
        return []

    # Convenience methods that match the AnimationIntegration interface
    def trigger_weapon_muzzle_flash(self, weapon, position):
        """Trigger muzzle flash effect for a weapon."""
        return self.trigger_endpoint(weapon, 'muzzle_flash', position)

    def trigger_weapon_impact(self, weapon, impact_position, damage_scale=1.0):
        """Trigger impact effect for a weapon."""
        return self.trigger_endpoint(weapon, 'impact_effect', impact_position, damage_scale=damage_scale)

    def trigger_projectile_trail(self, projectile):
        """Start trail effect for a projectile."""
        return self.trigger_endpoint(projectile, 'trail_effect')

    def trigger_projectile_impact(self, projectile, impact_position):
        """Trigger impact explosion for a projectile."""
        return self.trigger_endpoint(projectile, 'impact_explosion', impact_position)

    def trigger_projectile_timeout(self, projectile):
        """Trigger timeout explosion for a projectile."""
        return self.trigger_endpoint(projectile, 'timeout_explosion')

    def trigger_ship_engine_trail(self, ship):
        """Start engine trail effect for a ship."""
        return self.trigger_endpoint(ship, 'engine_trail')

    def trigger_ship_shield_hit(self, ship, impact_position):
        """Trigger shield hit effect for a ship."""
        return self.trigger_endpoint(ship, 'shield_hit', impact_position)

    def trigger_ship_destruction(self, ship):
        """Trigger destruction effect for a ship."""
        return self.trigger_endpoint(ship, 'destruction')

    def update_ship_damage_state(self, ship):
        """Check and update ship damage state effects."""
        return self.check_damage_state_triggers(ship)
