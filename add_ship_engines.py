#!/usr/bin/env python3
"""
Script to add engine trail animation endpoints to all ships
"""

import json

def add_engine_trails_to_ships():
    # Load ships data
    with open('ships_data.json', 'r') as f:
        ships_data = json.load(f)
    
    # Define engine trail positions based on ship size
    size_positions = {
        'small': [0, 15],
        'medium': [0, 20], 
        'large': [0, 25],
        'capital': [0, 30]
    }
    
    # Ships that already have animation endpoints
    ships_with_endpoints = ['scout']  # scout already has engine trail
    
    # Add engine trail to ships that don't have it
    for ship_id, ship_data in ships_data.items():
        if ship_id not in ships_with_endpoints:
            ship_size = ship_data.get('size', 'small')
            position = size_positions.get(ship_size, [0, 15])
            
            # Add animation endpoints
            if 'animation_endpoints' not in ship_data:
                ship_data['animation_endpoints'] = {}
            
            ship_data['animation_endpoints']['engine_trail'] = {
                'effect': 'engine_trail',
                'position': position,
                'trigger': 'on_thrust'
            }
            
            print(f"Added engine trail to {ship_id} ({ship_size}) at position {position}")
    
    # Save updated ships data
    with open('ships_data.json', 'w') as f:
        json.dump(ships_data, f, indent=2)
    
    print("All ships now have engine trail animation endpoints!")

if __name__ == "__main__":
    add_engine_trails_to_ships()
