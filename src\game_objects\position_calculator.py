"""
Position Calculator for Animation Endpoints

This module calculates actual pixel positions for animation effects based on:
- Entity position and rotation
- Position type (impact_point, ship_center, weapon_mount, etc.)
- Configured offsets
- Entity sprite dimensions
"""

import pygame as pg
import math
from typing import Tuple, Optional, Union


class PositionCalculator:
    """Calculates effect positions relative to game entities."""
    
    # Standard position types
    POSITION_TYPES = {
        'impact_point',      # Where projectile hit
        'ship_center',       # Center of ship sprite
        'ship_rear',         # Back of ship (calculated from sprite size + angle)
        'ship_front',        # Front of ship
        'weapon_mount',      # Where weapon is mounted on ship
        'projectile_center', # Center of projectile
        'projectile_rear',   # Behind projectile (for trails)
        'projectile_front',  # Front of projectile
        'entity_center',     # Generic center position
    }
    
    @staticmethod
    def calculate_position(entity, position_type: str, offset: Tuple[float, float] = (0, 0), 
                          impact_point: Optional[Tuple[float, float]] = None) -> pg.math.Vector2:
        """
        Calculate the actual pixel position for an effect.
        
        Args:
            entity: The game entity (ship, projectile, etc.)
            position_type: Type of position calculation
            offset: X, Y pixel offsets to apply
            impact_point: Specific impact point for impact_point position type
            
        Returns:
            Vector2 with the calculated position
        """
        if position_type not in PositionCalculator.POSITION_TYPES:
            print(f"Warning: Unknown position type '{position_type}', using entity_center")
            position_type = 'entity_center'
        
        # Get base position
        if position_type == 'impact_point' and impact_point:
            base_pos = pg.math.Vector2(impact_point)
        else:
            base_pos = PositionCalculator._get_entity_position(entity, position_type)
        
        # Apply offset
        if offset != (0, 0):
            # If entity has rotation, rotate the offset
            if hasattr(entity, 'angle') and entity.angle != 0:
                rotated_offset = PositionCalculator._rotate_offset(offset, entity.angle)
                base_pos += rotated_offset
            else:
                base_pos += pg.math.Vector2(offset)
        
        return base_pos
    
    @staticmethod
    def _get_entity_position(entity, position_type: str) -> pg.math.Vector2:
        """Get the base position for an entity based on position type."""
        if not hasattr(entity, 'pos'):
            return pg.math.Vector2(0, 0)
        
        base_pos = pg.math.Vector2(entity.pos)
        
        if position_type in ['entity_center', 'ship_center', 'projectile_center']:
            return base_pos
        
        # Get entity dimensions for relative positioning
        sprite_size = PositionCalculator._get_entity_sprite_size(entity)
        entity_angle = getattr(entity, 'angle', 0)
        
        if position_type == 'ship_rear':
            # Calculate rear position based on sprite size and rotation
            rear_offset = PositionCalculator._calculate_rear_offset(sprite_size, entity_angle)
            return base_pos + rear_offset
        
        elif position_type == 'ship_front':
            # Calculate front position
            front_offset = PositionCalculator._calculate_front_offset(sprite_size, entity_angle)
            return base_pos + front_offset
        
        elif position_type == 'projectile_rear':
            # For projectiles, rear is opposite to movement direction
            rear_offset = PositionCalculator._calculate_rear_offset(sprite_size, entity_angle)
            return base_pos + rear_offset
        
        elif position_type == 'projectile_front':
            # For projectiles, front is in movement direction
            front_offset = PositionCalculator._calculate_front_offset(sprite_size, entity_angle)
            return base_pos + front_offset
        
        elif position_type == 'weapon_mount':
            # For now, use ship front as weapon mount point
            # This could be enhanced later with specific mount point data
            front_offset = PositionCalculator._calculate_front_offset(sprite_size, entity_angle)
            return base_pos + front_offset
        
        return base_pos
    
    @staticmethod
    def _get_entity_sprite_size(entity) -> Tuple[int, int]:
        """Get the sprite size of an entity."""
        # Try to get size from sprite
        if hasattr(entity, 'image') and entity.image:
            return entity.image.get_size()
        
        # Try to get size from entity properties
        if hasattr(entity, 'sprite_size'):
            size = entity.sprite_size
            if isinstance(size, (int, float)):
                return (int(size), int(size))
            elif isinstance(size, (tuple, list)) and len(size) >= 2:
                return (int(size[0]), int(size[1]))
        
        # Try to get size from rect
        if hasattr(entity, 'rect'):
            return entity.rect.size
        
        # Default fallback size
        return (32, 32)
    
    @staticmethod
    def _calculate_rear_offset(sprite_size: Tuple[int, int], angle: float) -> pg.math.Vector2:
        """Calculate offset to the rear of an entity."""
        # Use half the sprite height as the rear distance
        rear_distance = sprite_size[1] / 2
        
        # Convert angle to radians and calculate offset
        # Rear is opposite to the facing direction
        angle_rad = math.radians(angle + 180)  # Add 180 for rear
        offset_x = math.cos(angle_rad) * rear_distance
        offset_y = math.sin(angle_rad) * rear_distance
        
        return pg.math.Vector2(offset_x, offset_y)
    
    @staticmethod
    def _calculate_front_offset(sprite_size: Tuple[int, int], angle: float) -> pg.math.Vector2:
        """Calculate offset to the front of an entity."""
        # Use half the sprite height as the front distance
        front_distance = sprite_size[1] / 2
        
        # Convert angle to radians and calculate offset
        angle_rad = math.radians(angle)
        offset_x = math.cos(angle_rad) * front_distance
        offset_y = math.sin(angle_rad) * front_distance
        
        return pg.math.Vector2(offset_x, offset_y)
    
    @staticmethod
    def _rotate_offset(offset: Tuple[float, float], angle: float) -> pg.math.Vector2:
        """Rotate an offset by the given angle."""
        angle_rad = math.radians(angle)
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        
        rotated_x = offset[0] * cos_a - offset[1] * sin_a
        rotated_y = offset[0] * sin_a + offset[1] * cos_a
        
        return pg.math.Vector2(rotated_x, rotated_y)
