"""
Simple AI System for Escape Velocity Py
Clean, effective AI with 4 states and group combat.
"""
import pygame as pg
import random
import math

# AI States
AI_STATE_PATROL = "PATROL"
AI_STATE_TRADING = "TRADING"  
AI_STATE_ATTACKING = "ATTACKING"
AI_STATE_FLEEING = "FLEEING"
AI_STATE_JUMPING_AWAY = "JUMPING_AWAY"
AI_STATE_CHARGING_HYPERDRIVE = "CHARGING_HYPERDRIVE"

# Ship Size Power Multipliers
SIZE_POWER = {
    "small": 1,
    "medium": 2, 
    "large": 3,
    "capital": 5
}

class SimpleAI:
    """Simple AI controller that replaces complex AI system."""
    
    def __init__(self, ai_ship):
        self.ship = ai_ship
        self.game = ai_ship.game
        
        # AI Behavior
        self.behavior = self._choose_spawn_behavior()
        self.state = AI_STATE_PATROL if self.behavior == "patrol" else AI_STATE_TRADING
        
        # Timers
        self.life_timer = random.randint(10, 300) * 60  # 10-300 seconds in frames
        self.scan_timer = 0
        self.state_timer = 0
        
        # Combat
        self.target = None
        self.waypoint = None
        
        # Hyperdrive charging system
        self.hyperdrive_charge_time = 0
        self.max_hyperdrive_charge_time = 180  # 3 seconds at 60 FPS
        self.is_charging_hyperdrive = False
        
        # Smart weapon selection system
        self.active_weapon = None
        self.weapon_switch_timer = 0
        self.weapon_switch_cooldown = 120  # 2 seconds before switching weapons
        
        print(f"SimpleAI: {self.ship.ship_type} spawned with {self.behavior} behavior, life: {self.life_timer/60:.1f}s")
    
    def _update_weapons(self, dt):
        """Update all weapons - handle cooldowns and maintenance."""
        if hasattr(self.ship, 'weapons'):
            for weapon in self.ship.weapons:
                if hasattr(weapon, 'update'):
                    weapon.update(dt)
    
    def _choose_spawn_behavior(self):
        """Choose what this ship will do in the system."""
        roll = random.random()
        if roll < 0.70:
            return "patrol"
        elif roll < 0.90:
            return "trading"
        else:
            return "patrol"  # Can add escort/other behaviors later
    
    def update(self, dt):
        """Main AI update loop."""
        # Update timers
        self.life_timer -= 1
        self.scan_timer -= 1
        self.state_timer -= 1
        self.weapon_switch_timer -= 1  # Weapon switching cooldown
        
        # Life timer expired - jump out
        if self.life_timer <= 0:
            self._start_jump_out()
            return
        
        # Scan for threats every 3 seconds
        if self.scan_timer <= 0:
            self._scan_for_threats()
            self.scan_timer = 180  # 3 seconds at 60 FPS
        
        # Update all weapons and select best weapon for current situation
        self._update_weapons(dt)
        if self.weapon_switch_timer <= 0:
            self._select_best_weapon()
            self.weapon_switch_timer = self.weapon_switch_cooldown
        
        # Execute current state
        if self.state == AI_STATE_PATROL:
            self._patrol_behavior()
        elif self.state == AI_STATE_TRADING:
            self._trading_behavior()
        elif self.state == AI_STATE_ATTACKING:
            self._attacking_behavior()
        elif self.state == AI_STATE_FLEEING:
            self._fleeing_behavior()
        elif self.state == AI_STATE_JUMPING_AWAY:
            self._jumping_away_behavior()
        elif self.state == AI_STATE_CHARGING_HYPERDRIVE:
            self._charging_hyperdrive_behavior()
    
    def _respond_to_attack(self, attacker_faction_id):
        """ALWAYS respond when attacked, regardless of faction standing."""
        print(f"DEBUG: {self.ship.ship_type} attacked by {attacker_faction_id} - responding immediately!")
        
        if self.state == AI_STATE_FLEEING or self.state == AI_STATE_JUMPING_AWAY:
            print(f"DEBUG: Already fleeing/jumping, ignoring attack")
            return  # Already fleeing or jumping away
        
        # CRITICAL: Interrupt hyperdrive charging if under attack
        if self.state == AI_STATE_CHARGING_HYPERDRIVE:
            print(f"DEBUG: Hyperdrive charge interrupted by attack! Switching to combat response.")
            self.is_charging_hyperdrive = False
            self.hyperdrive_charge_time = 0
        
        # Find the attacker
        attacker = None
        if attacker_faction_id == "player":
            attacker = self.game.player
            print(f"DEBUG: Attacker is player")
        else:
            # Find AI ship with matching faction
            for ship in self.game.ai_ships:
                if ship.faction_id == attacker_faction_id and ship != self.ship:
                    attacker = ship
                    print(f"DEBUG: Found AI attacker: {ship.ship_type}")
                    break
        
        if not attacker:
            print(f"DEBUG: Could not find attacker with faction {attacker_faction_id}")
            return
        
        # Calculate immediate threat for this specific attacker
        threat_ratio = self._calculate_single_threat(attacker)
        print(f"DEBUG: Threat ratio: {threat_ratio:.2f}")
        
        # ALWAYS respond when attacked - no faction check!
        if threat_ratio > 1.2:  # We're stronger - attack immediately
            self._switch_to_attacking(attacker)
            print(f"SimpleAI: {self.ship.ship_type} attacking {attacker_faction_id} (stronger, ratio: {threat_ratio:.2f})")
        elif threat_ratio < 0.8:  # They're stronger - flee immediately
            self._switch_to_fleeing()
            print(f"SimpleAI: {self.ship.ship_type} fleeing from {attacker_faction_id} (weaker, ratio: {threat_ratio:.2f})")
        else:
            # Close fight - attack anyway since we were attacked
            self._switch_to_attacking(attacker)
            print(f"SimpleAI: {self.ship.ship_type} fighting back against {attacker_faction_id} (even fight, ratio: {threat_ratio:.2f})")
    
    def _is_hostile_to_attacker(self, attacker, attacker_faction_id):
        """Check if attacker is hostile to us."""
        if attacker_faction_id == "player":
            return self._is_hostile_to_player()
        else:
            return self._is_hostile_to(attacker)
    
    def _calculate_single_threat(self, attacker):
        """Calculate threat ratio for single attacker (simplified)."""
        my_power = self._get_ship_power(self.ship)
        enemy_power = self._get_ship_power(attacker)
        return my_power / max(enemy_power, 1)
    
    def _scan_for_threats(self):
        """Scan for hostile ships and decide action."""
        if self.state == AI_STATE_FLEEING:
            return  # Already fleeing
        
        # Find nearby hostile ships
        hostiles = []
        for ship in self.game.ai_ships:
            if ship != self.ship and ship.alive():
                if self._is_hostile_to(ship):
                    distance = self.ship.pos.distance_to(ship.pos)
                    if distance < 600:  # Scan range
                        hostiles.append(ship)
        
        # Check player too
        if self.game.player:
            if self._is_hostile_to_player():
                distance = self.ship.pos.distance_to(self.game.player.pos)
                if distance < 600:
                    hostiles.append(self.game.player)
        
        if not hostiles:
            return
        
        # Calculate group threat with improved logic
        threat_ratio = self._calculate_group_threat(hostiles)
        
        # More aggressive threat thresholds - less fleeing
        if threat_ratio > 0.8:  # We have a fighting chance - attack!
            closest_hostile = min(hostiles, key=lambda h: self.ship.pos.distance_to(h.pos))
            self._switch_to_attacking(closest_hostile)
        elif threat_ratio < 0.4:  # Only flee if severely outmatched
            self._switch_to_fleeing()
        # Else: continue current behavior
    
    def _is_hostile_to(self, other_ship):
        """Check if other AI ship is hostile."""
        my_faction = self.ship.faction_id
        their_faction = other_ship.faction_id
        
        relations = self.game.faction_relations.get(my_faction, {})
        relation_value = relations.get(their_faction, 0.0)
        
        return relation_value < -0.5
    
    def _is_hostile_to_player(self):
        """Check if player is hostile."""
        my_faction = self.ship.faction_id
        player_faction = self.game.player_faction_id
        
        relations = self.game.faction_relations.get(my_faction, {})
        relation_value = relations.get(player_faction, 0.0)
        
        return relation_value < -0.5
    
    def _calculate_group_threat(self, hostiles):
        """Calculate threat ratio considering group combat."""
        # Calculate my side's power
        my_power = self._get_ship_power(self.ship)
        
        # Add nearby allies
        for ship in self.game.ai_ships:
            if ship != self.ship and ship.alive():
                if not self._is_hostile_to(ship):  # Ally
                    distance = self.ship.pos.distance_to(ship.pos)
                    if distance < 400:  # Alliance range
                        my_power += self._get_ship_power(ship)
        
        # Calculate enemy power
        enemy_power = 0
        for hostile in hostiles:
            enemy_power += self._get_ship_power(hostile)
        
        # Add enemy allies
        for ship in self.game.ai_ships:
            if ship not in hostiles and ship.alive():
                for hostile in hostiles:
                    if not self._ships_are_hostile(ship, hostile):  # They're allies
                        distance = hostile.pos.distance_to(ship.pos)
                        if distance < 400:
                            enemy_power += self._get_ship_power(ship)
                        break
        
        return my_power / max(enemy_power, 1)  # Avoid division by zero
    
    def _get_ship_power(self, ship):
        """Calculate ship's combat power more accurately."""
        # Handle player vs AI ships differently
        if hasattr(ship, 'ship'):  # AI ships
            size_mult = SIZE_POWER.get(ship.ship.size, 1)
            
            # Count actual weapon power, not just quantity
            weapon_power = 0
            weapon_count = 0
            for weapon in getattr(ship, 'weapons', []):
                # Only count weapons that can actually fire
                if hasattr(weapon, 'can_fire') and weapon.can_fire():
                    weapon_dps = getattr(weapon, 'shield_damage', 10) * getattr(weapon, 'fire_rate', 1.0)
                    weapon_power += weapon_dps
                    weapon_count += 1
                elif hasattr(weapon, 'shield_damage'):  # Weapon exists but maybe on cooldown
                    # Count reduced power for temporarily unavailable weapons
                    weapon_dps = getattr(weapon, 'shield_damage', 10) * getattr(weapon, 'fire_rate', 1.0)
                    weapon_power += weapon_dps * 0.7  # 70% power when not immediately available
                    weapon_count += 1
            
            # Baseline weapon power - every ship should have some fighting capability
            weapon_factor = max(weapon_power / 20, size_mult * 2)  # More generous baseline
            
            # Use health attribute (AI ships use this for armor)
            if hasattr(ship, 'health') and hasattr(ship, 'max_health'):
                health_percent = ship.health / ship.max_health
            else:
                health_percent = 1.0
                
        else:  # Player ship
            size_mult = SIZE_POWER.get(getattr(ship, 'size', 'small'), 1)
            
            # Player weapon power calculation
            weapon_power = 0
            for weapon in getattr(ship, 'weapons', []):
                if hasattr(weapon, 'can_fire') and weapon.can_fire():
                    weapon_dps = getattr(weapon, 'shield_damage', 10) * getattr(weapon, 'fire_rate', 1.0)
                    weapon_power += weapon_dps
                elif hasattr(weapon, 'shield_damage'):
                    weapon_dps = getattr(weapon, 'shield_damage', 10) * getattr(weapon, 'fire_rate', 1.0)
                    weapon_power += weapon_dps * 0.7
            
            weapon_factor = max(weapon_power / 20, size_mult * 2)
            
            # Player uses armor attribute
            if hasattr(ship, 'armor') and hasattr(ship, 'max_armor'):
                health_percent = ship.armor / ship.max_armor
            else:
                health_percent = 1.0
        
        total_power = weapon_factor * size_mult * health_percent
        # Reduced debug spam - only print occasionally
        if random.random() < 0.1:  # Only 10% of the time
            print(f"DEBUG: Ship power calc - {getattr(ship, 'ship_type', 'player')}: weapons={weapon_factor:.1f}, size={size_mult}, health={health_percent:.1f} = {total_power:.1f}")
        return total_power
    
    def _ships_are_hostile(self, ship1, ship2):
        """Check if two ships are hostile to each other."""
        relations = self.game.faction_relations.get(ship1.faction_id, {})
        relation_value = relations.get(ship2.faction_id, 0.0)
        return relation_value < -0.5
    
    def _switch_to_attacking(self, target):
        """Switch to attacking the target."""
        if self.state != AI_STATE_ATTACKING:
            self.state = AI_STATE_ATTACKING
            self.target = target
            self.state_timer = 0
            print(f"SimpleAI: {self.ship.ship_type} attacking {getattr(target, 'ship_type', 'player')}")
    
    def _switch_to_fleeing(self):
        """Switch to fleeing state."""
        if self.state != AI_STATE_FLEEING:
            self.state = AI_STATE_FLEEING
            self.target = None
            self.state_timer = 0
            print(f"SimpleAI: {self.ship.ship_type} fleeing")
    
    def _patrol_behavior(self):
        """Patrol around the system with smooth cruising."""
        if not self.waypoint or self.state_timer <= 0:
            # Pick new waypoint further away for smoother movement
            self.waypoint = pg.math.Vector2(
                random.randint(int(self.game.camera.width * 0.1), int(self.game.camera.width * 0.9)),
                random.randint(int(self.game.camera.height * 0.1), int(self.game.camera.height * 0.9))
            )
            self.state_timer = random.randint(600, 1800)  # 10-30 seconds per waypoint
        
        # Check if close to waypoint - pick new one before stopping
        distance_to_waypoint = self.ship.pos.distance_to(self.waypoint)
        if distance_to_waypoint < 100:  # Pick new waypoint before reaching current one
            self.state_timer = 0  # Force new waypoint selection
        
        # Fly toward waypoint with gentle movement
        self._fly_toward_point(self.waypoint, thrust_multiplier=0.3)
    
    def _trading_behavior(self):
        """Trade at planets."""
        if not self.waypoint:
            # Pick a random planet
            if self.game.planets:
                planet = random.choice(list(self.game.planets))
                self.waypoint = planet.pos.copy()
                self.state_timer = random.randint(600, 3600)  # 10-60 seconds at planet
        
        if self.waypoint:
            distance = self.ship.pos.distance_to(self.waypoint)
            if distance < 100:  # At planet
                if self.state_timer <= 0:
                    # Done trading, jump out
                    self._start_jump_out()
                else:
                    # Stay put and "trade"
                    self.ship.vel *= 0.8  # Slow down
            else:
                # Fly toward planet
                self._fly_toward_point(self.waypoint)
    
    def _attacking_behavior(self):
        """Attack the target."""
        if not self.target or not self.target.alive():
            # Target lost, return to patrol
            print(f"DEBUG: {self.ship.ship_type} target lost, returning to patrol")
            self.state = AI_STATE_PATROL
            self.target = None
            self.waypoint = None
            return
        
        # Face and approach target
        target_pos = self.target.pos
        distance = self.ship.pos.distance_to(target_pos)
        
        if distance > 200:  # Close to combat range
            self._fly_toward_point(target_pos)
        else:
            # Slow down when close
            self.ship.vel *= 0.8
        
        # Fire weapons if in range and available
        if distance < 300 and hasattr(self.ship, 'weapons'):
            weapons_fired = 0
            
            # Update all weapon timers first
            dt = 1.0 / 60.0  # Assume 60 FPS
            for weapon in self.ship.weapons:
                weapon.update(dt)
            
            # Use smart weapon selection - try active weapon first
            weapons_to_try = []
            if self.active_weapon and self.active_weapon in self.ship.weapons:
                weapons_to_try.append(self.active_weapon)
            
            # Add other weapons as backup
            for weapon in self.ship.weapons:
                if weapon != self.active_weapon:
                    weapons_to_try.append(weapon)
            
            for weapon in weapons_to_try:
                # Check if weapon is in range
                weapon_range = getattr(weapon, 'range', 300)
                if distance > weapon_range:
                    continue  # Skip out-of-range weapons
                
                # Enhanced weapon firing debug
                if hasattr(weapon, 'can_fire'):
                    can_fire = weapon.can_fire()
                    if not can_fire:
                        # Debug why weapon can't fire (reduced spam)
                        if random.random() < 0.1:  # Only debug 10% of the time
                            reasons = []
                            if hasattr(weapon, 'cooldown_timer') and weapon.cooldown_timer > 0:
                                reasons.append(f"cooldown: {weapon.cooldown_timer:.1f}s")
                            if hasattr(weapon, 'uses_ammo') and weapon.uses_ammo and getattr(weapon, 'current_ammo', 0) <= 0:
                                reasons.append("no ammo")
                            if reasons:
                                print(f"DEBUG: {self.ship.ship_type} weapon {weapon.name} can't fire: {', '.join(reasons)}")
                    else:
                        # Check if we need to aim for fixed weapons
                        can_fire_now = True
                        if hasattr(weapon, 'mount_type') and weapon.mount_type == "fixed":
                            # Fixed weapons need to be aimed at target
                            angle_to_target = math.degrees(math.atan2(
                                target_pos.y - self.ship.pos.y,
                                target_pos.x - self.ship.pos.x
                            )) + 90
                            angle_diff = abs((angle_to_target - self.ship.angle + 180) % 360 - 180)
                            if angle_diff > 20:  # Slightly more forgiving aiming
                                can_fire_now = False
                                # Aim the ship at the target
                                self.ship.turn_toward_angle(angle_to_target)
                        
                        if can_fire_now:
                            # CRITICAL: Actually create projectiles instead of just calling weapon.fire()
                            projectile = self._create_weapon_projectile(weapon, target_pos)
                            if projectile:
                                # Fire the weapon (handles cooldown)
                                success = weapon.fire()
                                if success:
                                    # Add projectile to game world
                                    self.game.all_sprites.add(projectile)
                                    # Ensure ship has projectiles group
                                    if not hasattr(self.ship, 'projectiles'):
                                        self.ship.projectiles = pg.sprite.Group()
                                    self.ship.projectiles.add(projectile)
                                    weapons_fired += 1
                                    if random.random() < 0.3:  # Reduce firing spam
                                        print(f"DEBUG: {self.ship.ship_type} fired {weapon.name} projectile ({'turret' if getattr(weapon, 'mount_type', 'fixed') == 'turret' else 'fixed'})")
                                    # Only fire one weapon per update to avoid spam
                                    break
                else:
                    # Weapon has no can_fire method - try to create projectile anyway
                    projectile = self._create_weapon_projectile(weapon, target_pos)
                    if projectile and hasattr(weapon, 'fire'):
                        success = weapon.fire()
                        if success:
                            # Add projectile to game world
                            self.game.all_sprites.add(projectile)
                            # Ensure ship has projectiles group
                            if not hasattr(self.ship, 'projectiles'):
                                self.ship.projectiles = pg.sprite.Group()
                            self.ship.projectiles.add(projectile)
                            weapons_fired += 1
                            print(f"DEBUG: {self.ship.ship_type} fired {weapon.name} projectile (no can_fire method)")
                            break
            
            # Only report firing failures occasionally to reduce spam
            if weapons_fired == 0 and random.random() < 0.05:
                print(f"DEBUG: {self.ship.ship_type} has {len(self.ship.weapons)} weapons but none could fire")
    
    def _fleeing_behavior(self):
        """Flee from combat by charging hyperdrive."""
        # Start hyperdrive charging instead of immediately jumping
        self._start_hyperdrive_charge()
    
    def _charging_hyperdrive_behavior(self):
        """Handle hyperdrive charging process."""
        # Stop moving and reduce velocity (preparing for jump)
        self.ship.vel *= 0.9
        
        # Increment charge time
        self.hyperdrive_charge_time += 1
        
        # Show charging progress
        charge_percent = (self.hyperdrive_charge_time / self.max_hyperdrive_charge_time) * 100
        if self.hyperdrive_charge_time % 30 == 0:  # Print every 0.5 seconds
            print(f"SimpleAI: {self.ship.ship_type} charging hyperdrive... {charge_percent:.0f}%")
        
        # Check if charge is complete
        if self.hyperdrive_charge_time >= self.max_hyperdrive_charge_time:
            print(f"SimpleAI: {self.ship.ship_type} hyperdrive charged! Jumping out.")
            self._execute_hyperdrive_jump()
    
    def _start_hyperdrive_charge(self):
        """Begin hyperdrive charging sequence."""
        print(f"SimpleAI: {self.ship.ship_type} beginning hyperdrive charge sequence")
        self.state = AI_STATE_CHARGING_HYPERDRIVE
        self.hyperdrive_charge_time = 0
        self.is_charging_hyperdrive = True
        self.target = None  # Stop attacking while charging
    
    def _execute_hyperdrive_jump(self):
        """Execute the actual hyperdrive jump."""
        self.is_charging_hyperdrive = False
        self.ship.kill()
        print(f"SimpleAI: {self.ship.ship_type} *FLASH* jumped to hyperspace!")
    
    def _jumping_away_behavior(self):
        """Move away from planets before jumping out."""
        if not self.waypoint:
            # No waypoint set, just jump immediately
            self._execute_jump()
            return
        
        # Fly toward the waypoint (away from planet)
        distance_to_waypoint = self.ship.pos.distance_to(self.waypoint)
        
        if distance_to_waypoint < 100:  # Close enough to waypoint
            # Check if we're far enough from all planets now
            min_jump_distance = 1500
            can_jump = True
            
            for planet in self.game.planets:
                distance = self.ship.pos.distance_to(planet.pos)
                if distance < min_jump_distance:
                    can_jump = False
                    break
            
            if can_jump:
                print(f"SimpleAI: {self.ship.ship_type} is now far enough from planets, executing jump")
                self._execute_jump()
            else:
                # Still too close, move further away
                print(f"SimpleAI: {self.ship.ship_type} still too close to planets, moving further away")
                # Find closest planet and move further away
                closest_planet = None
                closest_distance = float('inf')
                for planet in self.game.planets:
                    distance = self.ship.pos.distance_to(planet.pos)
                    if distance < closest_distance:
                        closest_distance = distance
                        closest_planet = planet
                
                if closest_planet:
                    away_direction = (self.ship.pos - closest_planet.pos).normalize()
                    self.waypoint = self.ship.pos + away_direction * (min_jump_distance + 300)
        
        # Fly toward waypoint
        self._fly_toward_point(self.waypoint, thrust_multiplier=1.0)
    
    def _fly_toward_point(self, target_pos, thrust_multiplier=1.0):
        """Fly toward a target position."""
        # Calculate direction
        direction = target_pos - self.ship.pos
        if direction.length() < 10:
            return
        
        direction.normalize_ip()
        
        # Apply thrust with multiplier for gentler movement
        thrust = direction * self.ship.acceleration * thrust_multiplier
        self.ship.vel += thrust
        
        # Face target
        target_angle = math.degrees(math.atan2(direction.y, direction.x)) + 90
        self.ship.turn_toward_angle(target_angle)
    
    def _select_best_weapon(self):
        """Intelligently select the best weapon for current situation."""
        if not hasattr(self.ship, 'weapons') or not self.ship.weapons:
            self.active_weapon = None
            return
        
        # If no target, prefer turrets for general use
        if not self.target:
            # Select any turret weapon that can fire
            for weapon in self.ship.weapons:
                if (hasattr(weapon, 'mount_type') and weapon.mount_type == "turret" and 
                    hasattr(weapon, 'can_fire') and weapon.can_fire()):
                    if self.active_weapon != weapon:
                        self.active_weapon = weapon
                        if random.random() < 0.2:  # Reduce debug spam
                            print(f"DEBUG: {self.ship.ship_type} selected turret weapon {weapon.name} for patrol")
                    return
            
            # No turrets available, use first weapon that can fire
            for weapon in self.ship.weapons:
                if hasattr(weapon, 'can_fire') and weapon.can_fire():
                    if self.active_weapon != weapon:
                        self.active_weapon = weapon
                        if random.random() < 0.2:
                            print(f"DEBUG: {self.ship.ship_type} selected weapon {self.active_weapon.name} for patrol")
                    return
            
            # If no weapons can fire, keep current or use first
            if not self.active_weapon:
                self.active_weapon = self.ship.weapons[0]
            return
        
        # We have a target - select weapon based on situation
        distance_to_target = self.ship.pos.distance_to(self.target.pos)
        
        # Weapon selection strategy:
        # 1. Prioritize weapons that can fire NOW
        # 2. Prefer turrets over fixed weapons
        # 3. Consider range and effectiveness
        # 4. Don't switch too frequently
        
        best_weapon = None
        best_score = 0
        
        for weapon in self.ship.weapons:
            score = 0
            
            # CRITICAL: Can the weapon fire right now?
            if hasattr(weapon, 'can_fire') and weapon.can_fire():
                score += 100  # Major bonus for ready weapons
            else:
                score += 20   # Some score for potentially useful weapons
            
            # Range check - weapon must be able to reach target
            weapon_range = getattr(weapon, 'range', 300)
            if distance_to_target > weapon_range:
                score = 0  # Weapon is out of range
                continue
            
            # Turrets are always better than fixed weapons
            if hasattr(weapon, 'mount_type') and weapon.mount_type == "turret":
                score += 50  # Large bonus for turrets
            
            # Weapon effectiveness (damage per second)
            shield_damage = getattr(weapon, 'shield_damage', 10)
            armor_damage = getattr(weapon, 'armor_damage', shield_damage)
            fire_rate = getattr(weapon, 'fire_rate', 1.0)
            weapon_dps = (shield_damage + armor_damage) * fire_rate
            score += weapon_dps / 5  # Scale weapon power appropriately
            
            # Fixed weapons need aiming consideration
            if hasattr(weapon, 'mount_type') and weapon.mount_type == "fixed":
                # Check if we're aimed at target
                angle_to_target = math.degrees(math.atan2(
                    self.target.pos.y - self.ship.pos.y,
                    self.target.pos.x - self.ship.pos.x
                )) + 90
                angle_diff = abs((angle_to_target - self.ship.angle + 180) % 360 - 180)
                if angle_diff <= 25:  # Well aimed
                    score += 30
                elif angle_diff <= 45:  # Reasonably aimed
                    score += 10
                else:
                    score -= 20  # Poorly aimed
            
            # Prefer weapons with ammo if it's a launcher
            if hasattr(weapon, 'uses_ammo') and weapon.uses_ammo:
                current_ammo = getattr(weapon, 'current_ammo', 0)
                max_ammo = getattr(weapon, 'max_ammo', 1)
                if current_ammo > 0:
                    ammo_ratio = current_ammo / max_ammo
                    score += ammo_ratio * 20  # Bonus for having ammo
                else:
                    score = 0  # No ammo = useless
                    continue
            
            # Stability bonus - don't switch weapons constantly
            if weapon == self.active_weapon:
                score += 15  # Bonus for staying with current weapon
            
            if score > best_score:
                best_score = score
                best_weapon = weapon
        
        # Switch weapons if new one is significantly better OR current weapon can't fire
        should_switch = False
        if not self.active_weapon:
            should_switch = True
        elif best_weapon != self.active_weapon:
            # Check if current weapon is ineffective
            current_can_fire = (hasattr(self.active_weapon, 'can_fire') and 
                              self.active_weapon.can_fire())
            
            if not current_can_fire:
                should_switch = True  # Switch if current weapon can't fire
            elif best_score > 50:  # Only switch if new weapon is much better
                should_switch = True
        
        if should_switch and best_weapon:
            old_weapon_name = self.active_weapon.name if self.active_weapon else "none"
            self.active_weapon = best_weapon
            if random.random() < 0.3:  # Reduce debug spam
                print(f"DEBUG: {self.ship.ship_type} switched from {old_weapon_name} to {best_weapon.name} (score: {best_score:.1f})")
        
        # Fallback: if no weapon selected, use first available
        if not self.active_weapon and self.ship.weapons:
            self.active_weapon = self.ship.weapons[0]
    
    def _create_weapon_projectile(self, weapon, target_pos):
        """Create actual projectile for weapon firing."""
        try:
            from game_objects.projectiles import LaserProjectile, MissileProjectile
            from game_objects.standardized_outfits import BEHAVIOR_INSTANT, BEHAVIOR_BEAM
            
            print(f"DEBUG: Creating projectile for weapon {weapon.name}")
            print(f"  - Mount type: {getattr(weapon, 'mount_type', 'unknown')}")
            print(f"  - Behavior: {getattr(weapon, 'projectile_behavior', 'unknown')}")
            print(f"  - Damage: {getattr(weapon, 'shield_damage', 0)}/{getattr(weapon, 'armor_damage', 0)}")
            print(f"  - Range: {getattr(weapon, 'range', 300)}")
            
            # Initialize ammo data variable
            ammo_data = None
            
            # Calculate spawn position and angle
            spawn_pos = self.ship.pos.copy()
            projectile_angle = self.ship.angle
            
            forward_angle = math.radians(self.ship.angle - 90)
            forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))
            
            if hasattr(weapon, 'mount_type') and weapon.mount_type == "fixed":
                # Fixed weapons fire from nose of ship
                offset_to_nose = self.ship.image_orig.get_height() / 2
                spawn_pos = self.ship.pos + forward * offset_to_nose
                print(f"  - Fixed weapon: spawn at nose offset {offset_to_nose}")
            elif hasattr(weapon, 'mount_type') and weapon.mount_type == "turret" and self.target:
                # Turret weapons aim at target
                to_target = target_pos - self.ship.pos
                if to_target.length() > 0:
                    target_angle = math.degrees(math.atan2(to_target.y, to_target.x)) + 90
                    projectile_angle = target_angle % 360
                    to_target.normalize_ip()
                    spawn_pos = self.ship.pos + to_target * (self.ship.rect.width / 3)
                    print(f"  - Turret weapon: aiming at angle {projectile_angle:.1f}")
            
            # LAUNCHER WEAPONS: Use ONLY the pre-loaded configured ammo
            if hasattr(weapon, 'uses_ammo') and weapon.uses_ammo:
                print(f"  - This is a LAUNCHER weapon, checking ammo...")
                
                # CRITICAL FIX: Use the configured ammo that was pre-loaded during ship creation
                ammo_data = None
                ammo_name = "No Ammo Configured"
                
                # Check if this launcher has pre-loaded ammo from configuration
                if hasattr(weapon, 'loaded_ammo') and weapon.loaded_ammo is not None:
                    ammo_data = weapon.loaded_ammo
                    ammo_name = ammo_data.name
                    print(f"  - Using PRE-LOADED ammo '{ammo_name}': damage={ammo_data.shield_damage}/{ammo_data.armor_damage}, behavior={ammo_data.projectile_behavior}")
                elif hasattr(weapon, 'loaded_ammo_id') and weapon.loaded_ammo_id:
                    # Fallback: try to get ammo by ID
                    try:
                        from game_objects.standardized_outfits import OUTFITS_REGISTRY
                        if weapon.loaded_ammo_id in OUTFITS_REGISTRY:
                            ammo_data = OUTFITS_REGISTRY[weapon.loaded_ammo_id]
                            ammo_name = ammo_data.name
                            print(f"  - Using CONFIGURED ammo '{ammo_name}' (by ID): damage={ammo_data.shield_damage}/{ammo_data.armor_damage}, behavior={ammo_data.projectile_behavior}")
                    except Exception as e:
                        print(f"  - Error loading configured ammo by ID: {e}")
                
                # STRICT: If no configured ammo found, launcher can't fire
                if ammo_data is None:
                    print(f"  - ❌ NO CONFIGURED AMMO for {weapon.name} - weapon cannot fire!")
                    print(f"  - ❌ This launcher needs ammunition in ship's default_outfits!")
                    return None  # Can't create projectile without ammo
                
                # Use ammo properties for damage and behavior
                weapon_shield_damage = getattr(ammo_data, 'shield_damage', 50)
                weapon_armor_damage = getattr(ammo_data, 'armor_damage', 50)
                projectile_behavior = getattr(ammo_data, 'projectile_behavior', 'dumbfire')
                
            else:
                # DIRECT FIRE WEAPONS: Use weapon's own properties
                weapon_shield_damage = getattr(weapon, 'shield_damage', 10)
                weapon_armor_damage = getattr(weapon, 'armor_damage', weapon_shield_damage)
                projectile_behavior = getattr(weapon, 'projectile_behavior', BEHAVIOR_INSTANT)
                
                if weapon_shield_damage == 0 and weapon_armor_damage == 0:
                    # Fallback to legacy damage field
                    legacy_damage = getattr(weapon, 'damage', 10)
                    weapon_shield_damage = legacy_damage
                    weapon_armor_damage = legacy_damage
                    print(f"  - Using legacy damage: {legacy_damage}")
            
            # Get weapon color from JSON data
            weapon_color = getattr(weapon, 'beam_color', (255, 0, 0))  # Default to red
            print(f"  - Weapon color: {weapon_color}")
            print(f"  - Final damage: {weapon_shield_damage}/{weapon_armor_damage}")
            print(f"  - Projectile behavior: {projectile_behavior}")
            print(f"  - Spawn position: {spawn_pos}")
            print(f"  - Projectile angle: {projectile_angle}")
            
            # Create appropriate projectile type based on behavior
            projectile = None
            
            if projectile_behavior == BEHAVIOR_INSTANT:
                # Standard laser projectile
                print(f"  - Creating INSTANT laser projectile")
                projectile = LaserProjectile(
                    self.game,
                    self.ship,
                    spawn_pos,
                    projectile_angle,
                    weapon_shield_damage,
                    weapon_armor_damage,
                    getattr(weapon, 'range', 300),
                    color=weapon_color
                )
            elif projectile_behavior == BEHAVIOR_BEAM:
                # Beam weapon
                print(f"  - Creating BEAM projectile")
                forward_angle = math.radians(projectile_angle - 90)
                forward_dir = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))
                beam_start = self.ship.pos + forward_dir * (self.ship.image_orig.get_height() / 2)
                
                projectile = LaserProjectile(
                    self.game,
                    self.ship,
                    beam_start,
                    projectile_angle,
                    weapon_shield_damage,
                    weapon_armor_damage,
                    getattr(weapon, 'range', 300),
                    color=weapon_color,
                    beam=True
                )
                # Configure as proper beam
                projectile.size = (getattr(weapon, 'range', 300), 4)
                projectile.lifetime = 0.05  # Very short flash
                projectile.speed = 0  # Stationary
                projectile.create_default_image()
                
                # Position beam correctly
                weapon_range = getattr(weapon, 'range', 300)
                beam_end = beam_start + forward_dir * weapon_range
                beam_center = (beam_start + beam_end) / 2
                projectile.pos = beam_center
                projectile.rect.center = beam_center
                
            elif projectile_behavior in ['dumbfire', 'guided', 'delayed', 'proximity'] or (hasattr(weapon, 'uses_ammo') and weapon.uses_ammo):
                # Missile/guided projectile from launcher or ammo behavior
                print(f"  - Creating MISSILE projectile with behavior: {projectile_behavior}")
                
                # Get tracking from ammo data if available
                tracking_strength = 0.0
                if ammo_data and hasattr(ammo_data, 'tracking_strength'):
                    tracking_strength = ammo_data.tracking_strength
                elif hasattr(weapon, 'tracking_strength'):
                    tracking_strength = weapon.tracking_strength
                
                # Get projectile sprite from ammo data
                projectile_sprite_path = None
                if ammo_data and hasattr(ammo_data, 'projectile_sprite'):
                    projectile_sprite_path = ammo_data.projectile_sprite
                    print(f"  - Using ammo sprite: {projectile_sprite_path}")
                elif hasattr(weapon, 'projectile_sprite'):
                    projectile_sprite_path = weapon.projectile_sprite
                    print(f"  - Using weapon sprite: {projectile_sprite_path}")
                
                # Get projectile speed from ammo
                projectile_speed = 300  # Default
                if ammo_data and hasattr(ammo_data, 'projectile_speed'):
                    projectile_speed = ammo_data.projectile_speed
                elif hasattr(weapon, 'projectile_speed'):
                    projectile_speed = weapon.projectile_speed
                
                projectile = MissileProjectile(
                    self.game,
                    self.ship,
                    spawn_pos,
                    projectile_angle,
                    weapon_shield_damage,
                    weapon_armor_damage,
                    getattr(weapon, 'range', 600),
                    self.target,
                    tracking=tracking_strength,
                    projectile_speed=projectile_speed,
                    image_path=projectile_sprite_path
                )
                # Consume ammo from launcher
                weapon.current_ammo -= 1
            else:
                # Default to laser projectile
                print(f"  - Creating DEFAULT laser projectile")
                projectile = LaserProjectile(
                    self.game,
                    self.ship,
                    spawn_pos,
                    projectile_angle,
                    weapon_shield_damage,
                    weapon_armor_damage,
                    getattr(weapon, 'range', 300),
                    color=weapon_color
                )
            
            if projectile:
                print(f"✅ Successfully created projectile: {type(projectile).__name__}")
                print(f"  - Color: {projectile.color}")
                print(f"  - Size: {projectile.size}")
                print(f"  - Position: {projectile.pos}")
            else:
                print(f"❌ Failed to create projectile - projectile is None")
            
            return projectile
            
        except Exception as e:
            print(f"ERROR: Failed to create projectile for {weapon.name}: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _start_jump_out(self):
        """Begin jump out sequence."""
        print(f"SimpleAI: {self.ship.ship_type} jumping out")
        
        # Check if we're too close to planets - move away first
        min_jump_distance = 1500
        closest_planet = None
        closest_distance = float('inf')
        
        for planet in self.game.planets:
            distance = self.ship.pos.distance_to(planet.pos)
            if distance < closest_distance:
                closest_distance = distance
                closest_planet = planet
        
        # If too close to any planet, move away first
        if closest_planet and closest_distance < min_jump_distance:
            # Move away from closest planet
            away_direction = (self.ship.pos - closest_planet.pos).normalize()
            self.waypoint = self.ship.pos + away_direction * (min_jump_distance + 200)  # Extra buffer
            self.state = AI_STATE_JUMPING_AWAY
            print(f"SimpleAI: {self.ship.ship_type} moving away from {closest_planet.name} before jumping")
            return
        
        # Far enough from planets - can jump immediately
        self._execute_jump()
    
    def _execute_jump(self):
        """Execute the actual jump sequence."""
        # Simple jump: stop, face random direction, disappear
        self.ship.vel *= 0.5  # Start slowing down
        
        if self.ship.vel.length() < 20:  # Nearly stopped
            # Face random direction
            self.ship.angle = random.uniform(0, 360)
            
            # Create simple jump effect (could be enhanced later)
            print(f"SimpleAI: {self.ship.ship_type} *FLASH* jumped out")
            
            # Remove ship
            self.ship.kill()
        else:
            # Still slowing down, continue until slow enough
            pass


def replace_ai_with_simple(ai_ship):
    """Replace complex AI with simple AI."""
    ai_ship.simple_ai = SimpleAI(ai_ship)
    return ai_ship.simple_ai
