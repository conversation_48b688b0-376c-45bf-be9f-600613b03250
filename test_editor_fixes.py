#!/usr/bin/env python3
"""
Test script to verify the editor fixes are working correctly.
Tests the launcher-ammo compatibility system and data persistence.
"""

import json
import sys
from pathlib import Path

def test_launcher_ammo_compatibility():
    """Test that launcher-ammo compatibility is working correctly."""
    print("🧪 Testing Launcher-Ammo Compatibility System...")
    
    # Load the outfits data
    outfits_file = Path("outfits_data.json")
    if not outfits_file.exists():
        print("❌ outfits_data.json not found!")
        return False
    
    with open(outfits_file, 'r') as f:
        outfits_data = json.load(f)
    
    # Find all launchers (weapons with uses_ammo: true)
    launchers = []
    for outfit_id, outfit in outfits_data.items():
        if outfit.get('category') == 'weapons' and outfit.get('uses_ammo', False):
            launchers.append(outfit_id)
    
    print(f"✅ Found {len(launchers)} launchers: {launchers}")
    
    # Find all ammunition
    ammunition = []
    for outfit_id, outfit in outfits_data.items():
        if outfit.get('category') == 'ammunition':
            ammunition.append(outfit_id)
    
    print(f"✅ Found {len(ammunition)} ammunition types: {ammunition}")
    
    # Check compatibility assignments
    compatibility_found = False
    for ammo_id in ammunition:
        ammo = outfits_data[ammo_id]
        compatible_launchers = ammo.get('compatible_launchers', [])
        if compatible_launchers:
            print(f"✅ {ammo['name']} is compatible with: {compatible_launchers}")
            compatibility_found = True
            
            # Verify that the compatible launchers actually exist and are launchers
            for launcher_id in compatible_launchers:
                if launcher_id not in launchers:
                    print(f"❌ ERROR: {ammo['name']} references non-existent launcher: {launcher_id}")
                    return False
        else:
            print(f"⚠️  {ammo['name']} has no compatible launchers assigned")
    
    if compatibility_found:
        print("✅ Launcher-ammo compatibility system is working!")
        return True
    else:
        print("⚠️  No ammunition has launcher compatibility assigned")
        return False

def test_window_sizing():
    """Test that window sizing is appropriate."""
    print("\n🧪 Testing Window Sizing...")
    
    # Check the main window configuration
    main_window_file = Path("editor_modules/main_window.py")
    if not main_window_file.exists():
        print("❌ main_window.py not found!")
        return False
    
    with open(main_window_file, 'r') as f:
        content = f.read()
    
    if "1600x1000" in content:
        print("✅ Window size increased to 1600x1000")
        return True
    else:
        print("❌ Window size not updated")
        return False

def test_data_persistence():
    """Test that data is being saved and loaded correctly."""
    print("\n🧪 Testing Data Persistence...")
    
    # Check that both JSON files exist
    outfits_file = Path("outfits_data.json")
    ships_file = Path("ships_data.json")
    
    if not outfits_file.exists():
        print("❌ outfits_data.json not found!")
        return False
    
    if not ships_file.exists():
        print("❌ ships_data.json not found!")
        return False
    
    # Load and validate JSON structure
    try:
        with open(outfits_file, 'r') as f:
            outfits_data = json.load(f)
        
        with open(ships_file, 'r') as f:
            ships_data = json.load(f)
        
        print(f"✅ Loaded {len(outfits_data)} outfits from JSON")
        print(f"✅ Loaded {len(ships_data)} ships from JSON")
        
        # Check that outfits have required fields
        for outfit_id, outfit in outfits_data.items():
            required_fields = ['id', 'name', 'category']
            for field in required_fields:
                if field not in outfit:
                    print(f"❌ Outfit {outfit_id} missing required field: {field}")
                    return False
        
        print("✅ All outfits have required fields")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing EscapeVelocityPy Editor Fixes")
    print("=" * 50)
    
    tests = [
        test_window_sizing,
        test_data_persistence,
        test_launcher_ammo_compatibility,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Editor fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
