"""
Data Manager for the Enhanced Content Editor
Handles loading, saving, and managing game data
"""

import json
import os
import sys
from pathlib import Path

def _format_beam_color(color):
    """Ensure beam color is in array format for JSON."""
    if isinstance(color, str):
        try:
            parts = color.split(',')
            if len(parts) >= 3:
                return [int(parts[0]), int(parts[1]), int(parts[2])]
        except (ValueError, IndexError):
            pass
        return [255, 0, 0]  # Default red
    elif isinstance(color, (tuple, list)):
        return list(color[:3])  # Convert to list and take first 3 elements
    else:
        return [255, 0, 0]  # Default red

def _format_beam_size(size):
    """Ensure beam size is in array format for JSON."""
    if isinstance(size, str):
        try:
            parts = size.split(',')
            if len(parts) >= 2:
                return [int(parts[0]), int(parts[1])]
        except (ValueError, IndexError):
            pass
        return [8, 2]  # Default size
    elif isinstance(size, (tuple, list)):
        return list(size[:2])  # Convert to list and take first 2 elements
    else:
        return [8, 2]  # Default size

class DataManager:
    """Manages all game data for the editor."""

    def __init__(self):
        self.outfits_registry = {}
        self.ships_registry = {}
        self.standardized_ships_registry = {}

        # Determine the game root directory (where JSON files should be)
        self.game_root = Path(__file__).parent.parent  # Go up from editor_modules to EscapeVelocityPy
        print(f"Editor Data Manager: Game root directory = {self.game_root}")

        # Initialize game data
        self.load_game_data()

    def load_game_data(self):
        """Load game data from the game's source files."""
        try:
            # Add the game's src directory to the path
            game_src_path = Path(__file__).parent.parent / "src"
            sys.path.insert(0, str(game_src_path))

            # Import game systems
            from game_objects.standardized_outfits import OUTFITS_REGISTRY
            from game_objects.ships import SHIPS
            from game_objects.standardized_ships import STANDARDIZED_SHIPS_REGISTRY

            # Import example outfits module to trigger loading (this loads from JSON or creates examples)
            import game_objects.example_outfits  # This triggers the outfit loading process

            self.outfits_registry = OUTFITS_REGISTRY
            self.ships_registry = SHIPS
            self.standardized_ships_registry = STANDARDIZED_SHIPS_REGISTRY

            print(f"Successfully loaded game data:")
            print(f"  - {len(self.outfits_registry)} outfits")
            print(f"  - {len(self.ships_registry)} ships")
            print(f"  - {len(self.standardized_ships_registry)} standardized ships")

            # If we have outfits but no JSON file, export them for next time
            if len(self.outfits_registry) > 0:
                outfits_json = self.game_root / "outfits_data.json"
                if not outfits_json.exists():
                    print("No outfits JSON found but outfits are loaded. Creating initial JSON file...")
                    self.save_outfits_data()

            # Load any saved data (this will update outfits with saved changes)
            self.load_saved_data()

        except ImportError as e:
            print(f"Error importing game systems: {e}")
            print("Make sure the game files are in the correct location")

    def load_saved_data(self):
        """Load previously saved outfit and ship data."""
        self.load_saved_outfits()
        self.load_saved_ships()

    def load_saved_outfits(self):
        """Load previously saved outfit data if available."""
        save_file = self.game_root / "outfits_data.json"
        print(f"Editor: Looking for outfits data at {save_file}")
        if save_file.exists():
            try:
                with open(save_file, 'r') as f:
                    saved_data = json.load(f)

                # Update existing outfits with saved data
                for outfit_id, outfit_data in saved_data.items():
                    if outfit_id in self.outfits_registry:
                        outfit = self.outfits_registry[outfit_id]
                        self._update_outfit_from_data(outfit, outfit_data)

                print(f"Loaded saved outfit data from {save_file}")
            except Exception as e:
                print(f"Failed to load saved outfit data: {e}")

    def load_saved_ships(self):
        """Load previously saved ship data if available."""
        save_file = self.game_root / "ships_data.json"
        print(f"Editor: Looking for ships data at {save_file}")
        if save_file.exists():
            try:
                with open(save_file, 'r') as f:
                    saved_data = json.load(f)

                # Update existing ships with saved data
                for ship_id, ship_data in saved_data.items():
                    if ship_id in self.ships_registry:
                        ship = self.ships_registry[ship_id]
                        self._update_ship_from_data(ship, ship_data)

                print(f"Loaded saved ship data from {save_file}")
            except Exception as e:
                print(f"Failed to load saved ship data: {e}")

    def _update_outfit_from_data(self, outfit, outfit_data):
        """Update an outfit object with data from JSON."""
        # Update basic properties
        for prop in ['name', 'cost', 'space_required', 'min_tech_level', 'outfitter_icon', 'outfitter_image', 'description']:
            if prop in outfit_data:
                # Always set outfitter_image even if the attribute doesn't exist yet
                if prop == 'outfitter_image' or hasattr(outfit, prop):
                    setattr(outfit, prop, outfit_data[prop])

        # Update category-specific properties based on outfit category
        if hasattr(outfit, 'category'):
            if outfit.category == "weapons":
                for prop in ['mount_type', 'damage', 'fire_rate', 'range', 'energy_usage', 'uses_ammo', 'ammo_type', 'max_ammo', 'fire_sound', 
                            'shield_damage', 'armor_damage', 'projectile_behavior', 'projectile_speed', 'tracking_strength', 
                            'proximity_radius', 'delay_time', 'beam_color', 'beam_size', 'power_cost']:
                    if prop in outfit_data:
                        value = outfit_data[prop]
                        
                        # Convert JSON arrays back to comma-separated strings for UI display
                        if prop == 'beam_color' and isinstance(value, list) and len(value) >= 3:
                            value = f"{value[0]},{value[1]},{value[2]}"
                        elif prop == 'beam_size' and isinstance(value, list) and len(value) >= 2:
                            value = f"{value[0]},{value[1]}"
                        
                        # Always set fire_sound even if the attribute doesn't exist yet
                        if prop == 'fire_sound' or hasattr(outfit, prop):
                            setattr(outfit, prop, value)
                        else:
                            # For new properties, set them anyway
                            setattr(outfit, prop, value)
            elif outfit.category == "ammunition":
                for prop in ['quantity', 'shield_damage', 'armor_damage', 'projectile_speed', 'range', 'projectile_behavior', 'tracking_strength', 'delay_time', 'explosion_radius', 'projectile_sprite', 'compatible_launchers']:
                    if prop in outfit_data:
                        # Handle legacy 'damage' field
                        if prop in ['shield_damage', 'armor_damage'] and prop not in outfit_data and 'damage' in outfit_data:
                            setattr(outfit, prop, outfit_data['damage'])
                        else:
                            setattr(outfit, prop, outfit_data[prop])
                            # Load compatible_launchers without debug spam
                            pass
            elif outfit.category == "defense":
                for prop in ['shield_boost', 'armor_boost', 'shield_recharge_boost', 'damage_reduction']:
                    if prop in outfit_data and hasattr(outfit, prop):
                        setattr(outfit, prop, outfit_data[prop])
        
        # CRITICAL FIX: Load animation endpoints for all outfit types
        if 'animation_endpoints' in outfit_data:
            setattr(outfit, 'animation_endpoints', outfit_data['animation_endpoints'])

    def _update_ship_from_data(self, ship, ship_data):
        """Update a ship object with data from JSON."""
        # Update all ship properties - set them even if they don't exist yet
        for prop, value in ship_data.items():
            setattr(ship, prop, value)

    def save_outfits_data(self, filename="outfits_data.json"):
        """Save all outfit data to JSON file."""
        try:
            # Always save to the game root directory
            save_path = self.game_root / filename
            print(f"Editor: Saving outfits data to {save_path}")

            outfits_data = {}
            for outfit_id, outfit in self.outfits_registry.items():
                outfits_data[outfit_id] = self._outfit_to_dict(outfit)

            with open(save_path, 'w') as f:
                json.dump(outfits_data, f, indent=2)

            print(f"Saved {len(outfits_data)} outfits to {save_path}")
            return True
        except Exception as e:
            print(f"Failed to save outfits: {e}")
            return False

    def save_ships_data(self, filename="ships_data.json"):
        """Save all ship data to JSON file."""
        try:
            # Always save to the game root directory
            save_path = self.game_root / filename
            print(f"Editor: Saving ships data to {save_path}")

            ships_data = {}
            for ship_id, ship in self.ships_registry.items():
                ships_data[ship_id] = self._ship_to_dict(ship)

            with open(save_path, 'w') as f:
                json.dump(ships_data, f, indent=2)

            print(f"Saved {len(ships_data)} ships to {save_path}")
            return True
        except Exception as e:
            print(f"Failed to save ships: {e}")
            return False

    def _outfit_to_dict(self, outfit):
        """Convert an outfit object to a dictionary for JSON export."""
        outfit_data = {
            'id': getattr(outfit, 'id', outfit.name.lower().replace(' ', '_')),
            'name': outfit.name,
            'category': getattr(outfit, 'category', 'unknown'),
            'subcategory': getattr(outfit, 'subcategory', ''),
            'cost': getattr(outfit, 'cost', 1000),
            'space_required': getattr(outfit, 'space_required', 1),
            'min_tech_level': getattr(outfit, 'min_tech_level', 1),
            'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
            'outfitter_image': getattr(outfit, 'outfitter_image', ''),
            'description': getattr(outfit, 'description', ''),
        }

        # Add category-specific properties
        if hasattr(outfit, 'category'):
            if outfit.category == "weapons":
                # Base weapon properties
                weapon_data = {
                    'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                    'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                    'range': getattr(outfit, 'range', 300),
                    'energy_usage': getattr(outfit, 'energy_usage', 5),
                    'uses_ammo': getattr(outfit, 'uses_ammo', False),
                    'ammo_type': getattr(outfit, 'ammo_type', ''),
                    'max_ammo': getattr(outfit, 'max_ammo', 0),
                    'fire_sound': getattr(outfit, 'fire_sound', '')
                }
                
                # Only save damage/projectile properties for direct-fire weapons
                if not getattr(outfit, 'uses_ammo', False):
                    weapon_data.update({
                        'shield_damage': getattr(outfit, 'shield_damage', getattr(outfit, 'damage', 10)),
                        'armor_damage': getattr(outfit, 'armor_damage', getattr(outfit, 'damage', 10)),
                        'projectile_behavior': getattr(outfit, 'projectile_behavior', 'instant'),
                        'projectile_speed': getattr(outfit, 'projectile_speed', 800),
                        'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                        'proximity_radius': getattr(outfit, 'proximity_radius', 0),
                        'delay_time': getattr(outfit, 'delay_time', 0.0),
                        'beam_color': _format_beam_color(getattr(outfit, 'beam_color', '255,0,0')),
                        'beam_size': _format_beam_size(getattr(outfit, 'beam_size', '8,2')),
                        'power_cost': getattr(outfit, 'power_cost', 5.0)
                    })

                else:
                    # Launchers only get power cost
                    weapon_data['power_cost'] = getattr(outfit, 'power_cost', 10.0)
                
                outfit_data.update(weapon_data)
            elif outfit.category == "ammunition":
                outfit_data.update({
                    'quantity': getattr(outfit, 'quantity', 10),
                    'shield_damage': getattr(outfit, 'shield_damage', getattr(outfit, 'damage', 50)),
                    'armor_damage': getattr(outfit, 'armor_damage', getattr(outfit, 'damage', 50)),
                    'projectile_speed': getattr(outfit, 'projectile_speed', 8),
                    'range': getattr(outfit, 'range', 600),  # CRITICAL FIX: Add missing range field
                    'projectile_behavior': getattr(outfit, 'projectile_behavior', 'dumbfire'),
                    'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                    'delay_time': getattr(outfit, 'delay_time', 0.0),
                    'explosion_radius': getattr(outfit, 'explosion_radius', 20),
                    'projectile_sprite': getattr(outfit, 'projectile_sprite', ''),
                    'compatible_launchers': getattr(outfit, 'compatible_launchers', [])
                })
                # Save compatible_launchers without debug spam
                pass
            elif outfit.category == "defense":
                outfit_data.update({
                    'shield_boost': getattr(outfit, 'shield_boost', 0),
                    'armor_boost': getattr(outfit, 'armor_boost', 0),
                    'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                    'damage_reduction': getattr(outfit, 'damage_reduction', 0.0)
                })

        # CRITICAL FIX: Add animation endpoints for all outfit types
        if hasattr(outfit, 'animation_endpoints'):
            outfit_data['animation_endpoints'] = outfit.animation_endpoints

        return outfit_data

    def _ship_to_dict(self, ship):
        """Convert a ship object to a dictionary for JSON export."""
        return {
            # Basic properties
            'id': getattr(ship, 'id', ship.name.lower().replace(' ', '_')),
            'name': ship.name,
            'ship_class': getattr(ship, 'ship_class', 'fighter'),
            'size': getattr(ship, 'size', 'small'),
            'cost': getattr(ship, 'cost', 10000),
            'min_tech_level': getattr(ship, 'min_tech_level', 1),
            
            # Physical properties
            'outfit_space': getattr(ship, 'outfit_space', 20),
            'cargo_space': getattr(ship, 'cargo_space', 10),
            'mass': getattr(ship, 'mass', 1.0),
            
            # Performance properties
            'max_speed': getattr(ship, 'max_speed', 3.0),
            'acceleration': getattr(ship, 'acceleration', 0.5),
            'turn_rate': getattr(ship, 'turn_rate', 1.0),
            
            # Defense properties
            'shields': getattr(ship, 'shields', 50),
            'max_shields': getattr(ship, 'max_shields', 50),
            'armor': getattr(ship, 'armor', 30),
            'max_armor': getattr(ship, 'max_armor', 30),
            'shield_recharge_rate': getattr(ship, 'shield_recharge_rate', 1.0),
            
            # Power system properties
            'power_capacity': getattr(ship, 'power_capacity', 100),
            'power_regen_rate': getattr(ship, 'power_regen_rate', 5.0),
            'thruster_power_cost': getattr(ship, 'thruster_power_cost', 8.0),
            'weapon_power_cost_base': getattr(ship, 'weapon_power_cost_base', 12.0),
            'shield_regen_power_cost': getattr(ship, 'shield_regen_power_cost', 3.0),
            
            # Fuel system properties
            'fuel_capacity': getattr(ship, 'fuel_capacity', 150),
            'fuel_consumption_rate': getattr(ship, 'fuel_consumption_rate', 1.0),
            'jump_fuel_cost': getattr(ship, 'jump_fuel_cost', 25.0),
            
            # Visual properties
            'shipyard_image': getattr(ship, 'shipyard_image', ''),
            'shipyard_display_image': getattr(ship, 'shipyard_display_image', ''),
            'sprite': getattr(ship, 'sprite', ''),
            'sprite_size': getattr(ship, 'sprite_size', 32),
            'animation_frames': getattr(ship, 'animation_frames', 32),
            
            # Manufacturer properties
            'manufacturer': getattr(ship, 'manufacturer', 'Independent Shipyards'),
            'origin': getattr(ship, 'origin', 'Core Worlds'),
            'availability': getattr(ship, 'availability', 'common'),
            
            # Equipment properties
            'default_outfits': getattr(ship, 'default_outfits', {}),
            
            # Description
            'description': getattr(ship, 'description', ''),
            
            # CRITICAL FIX: Add animation endpoints for ships
        }
        
        # Add animation endpoints if they exist
        if hasattr(ship, 'animation_endpoints'):
            ship_data['animation_endpoints'] = ship.animation_endpoints
            
        return ship_data

    def get_outfits_by_category(self, category):
        """Get all outfits of a specific category."""
        return {
            outfit_id: outfit for outfit_id, outfit in self.outfits_registry.items()
            if (hasattr(outfit, 'category') and outfit.category == category) or
               (hasattr(outfit, '__class__') and category.title() in str(outfit.__class__))
        }

    def get_all_ships(self):
        """Get all ships."""
        return self.ships_registry if self.ships_registry else self.standardized_ships_registry

    def auto_save(self):
        """Auto-save all data with recursion protection."""
        if hasattr(self, '_auto_saving') and self._auto_saving:
            print("DEBUG: Auto-save already in progress, skipping to prevent recursion")
            return
        
        try:
            self._auto_saving = True
            self.save_outfits_data()
            self.save_ships_data()
        finally:
            self._auto_saving = False
        
    def reload_single_outfit(self, outfit_id):
        """Reload a single outfit from JSON data to ensure fresh data."""
        try:
            outfits_json = self.game_root / "outfits_data.json"
            if outfits_json.exists():
                with open(outfits_json, 'r') as f:
                    saved_data = json.load(f)
                
                # Update the specific outfit if it exists in JSON and in registry
                if outfit_id in saved_data and outfit_id in self.outfits_registry:
                    outfit = self.outfits_registry[outfit_id]
                    outfit_data = saved_data[outfit_id]
                    
                    # Force update this outfit with fresh JSON data
                    self._update_outfit_from_data(outfit, outfit_data)
                    
                    return True
        except Exception as e:
            print(f"DEBUG: Error reloading outfit {outfit_id}: {e}")
        return False
