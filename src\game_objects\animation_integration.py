"""
Animation Integration Module

This module connects the animation endpoint system to the main game,
loading the endpoints from JSON data and providing the interface for 
triggering animations throughout the game.
"""

from .animation_endpoint_manager import AnimationEndpointManager
import json
import os


def initialize_animation_system(game):
    """Initialize the animation system and load endpoints from JSON data."""
    print("Initializing animation endpoint system...")
    
    # Create the animation endpoint manager
    animation_manager = AnimationEndpointManager(game.effects_manager)
    
    # Load endpoints from data files
    try:
        # Lo<PERSON> outfits data
        outfits_data = {}
        if os.path.exists("outfits_data.json"):
            with open("outfits_data.json", 'r') as f:
                outfits_data = json.load(f)
            print(f"Loaded outfits data: {len(outfits_data)} items")
        
        # Load ships data  
        ships_data = {}
        if os.path.exists("ships_data.json"):
            with open("ships_data.json", 'r') as f:
                ships_data = json.load(f)
            print(f"Loaded ships data: {len(ships_data)} items")
        
        # Load endpoints into the manager
        animation_manager.load_endpoints_from_data(outfits_data, ships_data)
        
        print("Animation system initialized successfully!")
        return animation_manager
        
    except Exception as e:
        print(f"Error initializing animation system: {e}")
        return animation_manager  # Return manager even if loading failed


def trigger_weapon_fire(animation_manager, weapon_entity):
    """Trigger weapon fire animations."""
    if animation_manager:
        animation_manager.trigger_endpoint(weapon_entity, "muzzle_flash")


def trigger_projectile_hit(animation_manager, projectile_entity, hit_position=None):
    """Trigger projectile impact animations."""
    if animation_manager:
        # Try weapon impact effect first
        if not animation_manager.trigger_endpoint(projectile_entity, "impact_effect", hit_position):
            # Fall back to ammo impact explosion
            animation_manager.trigger_endpoint(projectile_entity, "impact_explosion", hit_position)


def trigger_projectile_timeout(animation_manager, projectile_entity):
    """Trigger projectile timeout animations."""
    if animation_manager:
        animation_manager.trigger_endpoint(projectile_entity, "timeout_explosion")


def trigger_ship_engine_trail(animation_manager, ship_entity, enable=True):
    """Start/stop ship engine trail animations."""
    if animation_manager:
        if enable:
            animation_manager.trigger_endpoint(ship_entity, "engine_trail")
        else:
            # Stop continuous effect
            for effect in animation_manager.continuous_effects[:]:
                if (effect.entity == ship_entity and 
                    effect.endpoint.name == "engine_trail"):
                    effect.effect_instance.finished = True
                    animation_manager.continuous_effects.remove(effect)


def trigger_ship_damage_effects(animation_manager, ship_entity):
    """Check and trigger ship damage state effects."""
    if animation_manager:
        animation_manager.check_damage_state_triggers(ship_entity)


def trigger_ship_destruction(animation_manager, ship_entity):
    """Trigger ship destruction animations."""
    if animation_manager:
        animation_manager.trigger_endpoint(ship_entity, "destruction")


def update_continuous_effects(animation_manager, entity, dt):
    """Update continuous effects for an entity."""
    if animation_manager:
        animation_manager.update_continuous_effects(entity, dt)


def cleanup_entity_effects(animation_manager, entity):
    """Clean up all effects for an entity."""
    if animation_manager:
        animation_manager.cleanup_entity_effects(entity)
