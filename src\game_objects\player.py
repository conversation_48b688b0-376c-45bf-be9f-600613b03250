"""
Updated player.py to work with the new standardized outfit system
"""
import pygame as pg
import math
import random
import copy
from game_objects.ships import get_ship_by_id
from game_objects.standardized_outfits import *
from game_objects.outfit_data_loader import load_outfits_from_data_files

# Load outfits from JSON data on import
load_outfits_from_data_files()
from game_objects.projectiles import LaserProjectile, MissileProjectile
from game_objects.sprite_manager import load_sprite, get_angle_from_vector
from game_objects.outfit_rules_engine import update_all_outfit_systems

# --- Constants ---
DRAG_FACTOR = 0.002  # Reduced drag for more persistent momentum

# Color constants
RED = (255, 0, 0)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
WHITE = (255, 255, 255)

class Player(pg.sprite.Sprite):
    def __init__(self, game, player_name="Player", ship_name="Ship", ship_id="scout"):
        super().__init__()
        self.game = game
        self.player_name = player_name
        self.ship_name = ship_name
        
        # Store the ship_id for animation system
        self._ship_id = ship_id

        # Get the ship from the ships module
        self.ship = get_ship_by_id(ship_id)
        if not self.ship:
            self.ship = get_ship_by_id("scout")
            self._ship_id = "scout"

        # Try to load the ship's spritesheet
        print(f"Attempting to load spritesheet for ship: {ship_id} (size: {self.ship.size})")
        self.spritesheet = load_sprite("ship", ship_id, self.ship.size)

        # Set up the ship image
        if self.spritesheet and self.spritesheet.image:
            print(f"Using spritesheet for ship: {ship_id}")
            self.image_orig = self.spritesheet.get_frame(0)
            if not self.image_orig:
                print(f"Failed to get frame 0 from spritesheet for ship: {ship_id}")
                if self.ship.image:
                    print(f"Falling back to ship's static image")
                    self.image_orig = self.ship.image
                else:
                    print(f"Creating default image for ship: {ship_id}")
                    self.image_orig = pg.Surface([30, 20])
                    self.image_orig.fill((0, 255, 0))
        elif self.ship.image:
            print(f"No spritesheet found, using ship's static image for: {ship_id}")
            self.image_orig = self.ship.image
        else:
            print(f"No spritesheet or static image found, creating default for: {ship_id}")
            self.image_orig = pg.Surface([30, 20])
            self.image_orig.fill((0, 255, 0))

        self.image_orig.set_colorkey((0, 0, 0))
        self.image = self.image_orig.copy()
        self.rect = self.image.get_rect()

        # Ship stats from base ship
        self.base_max_shields = self.ship.max_shields
        self.base_shields = self.ship.shields
        self.base_max_armor = self.ship.max_armor
        self.base_armor = self.ship.armor
        self.base_acceleration = self.ship.acceleration
        self.base_turn_rate = self.ship.turn_rate
        self.base_max_speed = self.ship.max_speed

        # Current stats (modified by outfits)
        self.max_shields = self.base_max_shields
        self.shields = self.base_shields
        self.max_armor = self.base_max_armor
        self.armor = self.base_armor
        self.acceleration = self.base_acceleration
        self.turn_rate = self.base_turn_rate
        self.max_speed = self.base_max_speed

        # NEW: Power and Fuel Systems from ship
        self.power_capacity = self.ship.power_capacity
        self.power = self.power_capacity
        self.power_regen_rate = self.ship.power_regen_rate
        self.fuel_capacity = self.ship.fuel_capacity
        self.fuel = self.fuel_capacity

        # Outfit system
        self.cargo_space = self.ship.cargo_space
        self.outfit_space = self.ship.outfit_space
        self.used_outfit_space = 0
        self.installed_outfits = {}  # outfit_id -> quantity
        self.cargo = {}  # Dictionary to store cargo items and quantities

        # Fleet management
        self.fleet_ships = []

        # Player economy
        self.credits = 500000

        # Position
        self.pos = pg.math.Vector2(self.game.screen.get_width() // 2, self.game.screen.get_height() - 50)
        self.rect.center = self.pos

        # Velocity and angle
        self.vel = pg.math.Vector2(0, 0)
        self.angle = 0

        # Faction ID for projectile ownership
        self.faction_id = "player"

        # Shield recharge system
        self.shield_recharge_delay = 180
        self.shield_recharge_timer = 0

        # Weapon systems - CLEAN SEPARATION
        self.weapons = []  # List of equipped weapon outfits
        self.active_weapon_index = 0
        self.projectiles = pg.sprite.Group()
        self.last_fire_time = 0
        
        # Ammo inventory system
        self.available_ammo_types = {}  # Dict of ammo_id -> ammo_outfit for inventory

        # Initialize with some default outfits from JSON data
        self.install_outfit("laser_cannon")
        self.install_outfit("Light Laser Turret")  # Add turret for testing
        self.install_outfit("basicshieldbooster")
        self.install_outfit("misslerack")
        self.load_ammo("missle")  # Load some missiles
        self.load_ammo("smartmissle")  # Load some smart missiles

    def consume_power(self, amount):
        """Consume power from the ship's power reserves."""
        if self.power >= amount:
            self.power -= amount
            return True
        return False

    def consume_fuel(self, amount):
        """Consume fuel from the ship's fuel reserves."""
        if self.fuel >= amount:
            self.fuel -= amount
            return True
        return False

    def regenerate_power(self, dt):
        """Regenerate power over time."""
        if self.power < self.power_capacity:
            regen_amount = self.power_regen_rate * dt
            self.power = min(self.power_capacity, self.power + regen_amount)

    def install_outfit(self, outfit_id):
        """Install an outfit on the ship."""
        outfit_template = get_outfit_by_id(outfit_id)
        if not outfit_template:
            self.game.set_status_message(f"Outfit '{outfit_id}' not found.", (255, 100, 100))
            return False

        # Check if outfit can be installed
        if not outfit_template.can_install_on_ship(self.ship):
            self.game.set_status_message(f"Cannot install {outfit_template.name} on this ship.", (255, 100, 100))
            return False

        # Check outfit space
        if self.used_outfit_space + outfit_template.space_required > self.outfit_space:
            self.game.set_status_message(f"Not enough outfit space for {outfit_template.name}.", (255, 100, 100))
            return False

        # Install the outfit
        if outfit_id in self.installed_outfits:
            self.installed_outfits[outfit_id] += 1
        else:
            self.installed_outfits[outfit_id] = 1

        self.used_outfit_space += outfit_template.space_required

        # Apply outfit effects
        self._recalculate_ship_stats()
        
        # CRITICAL FIX: Auto-load compatible ammo for newly installed launchers
        if hasattr(outfit_template, 'uses_ammo') and outfit_template.uses_ammo:
            self._auto_load_compatible_ammo(outfit_template)

        self.game.set_status_message(f"Installed {outfit_template.name}.", (100, 255, 100))
        return True

    def _auto_load_compatible_ammo(self, launcher_outfit):
        """Automatically load compatible ammo for a newly installed launcher."""
        launcher_id = launcher_outfit.id
        
        # Find all available ammo types that are compatible with this launcher
        compatible_ammo = []
        for ammo_id, ammo_outfit in self.available_ammo_types.items():
            compatible_launchers = getattr(ammo_outfit, 'compatible_launchers', [])
            if launcher_id in compatible_launchers and ammo_outfit.quantity > 0:
                compatible_ammo.append((ammo_id, ammo_outfit))
        
        if not compatible_ammo:
            # No compatible ammo available, just report that launcher is empty
            return
        
        # Find the best ammo to load (prefer first available)
        best_ammo_id, best_ammo = compatible_ammo[0]
        
        # Load ammo into any empty launchers of this type
        for weapon in self.weapons:
            if (weapon.id == launcher_id and 
                getattr(weapon, 'uses_ammo', False) and
                getattr(weapon, 'current_ammo', 0) == 0):  # Only load if empty
                
                max_ammo = getattr(weapon, 'max_ammo', 0)
                available_qty = best_ammo.quantity
                ammo_to_load = min(available_qty, max_ammo)
                
                if ammo_to_load > 0:
                    weapon.current_ammo = ammo_to_load
                    weapon.loaded_ammo_type = best_ammo_id
                    best_ammo.quantity -= ammo_to_load
                    
                    print(f"Auto-loaded {ammo_to_load}x {best_ammo.name} into {weapon.name}")
                    break

    def remove_outfit(self, outfit_id):
        """Remove an outfit from the ship."""
        if outfit_id not in self.installed_outfits or self.installed_outfits[outfit_id] <= 0:
            return False

        outfit_template = get_outfit_by_id(outfit_id)
        if not outfit_template:
            return False

        # Remove one instance
        self.installed_outfits[outfit_id] -= 1
        if self.installed_outfits[outfit_id] == 0:
            del self.installed_outfits[outfit_id]

        self.used_outfit_space -= outfit_template.space_required

        # Recalculate ship stats
        self._recalculate_ship_stats()
        return True

    def _find_nearest_enemy(self):
        """Find the nearest hostile ship for auto-targeting."""
        nearest_enemy = None
        min_distance = float('inf')
        
        for ship in self.game.ai_ships:
            if not ship.alive():
                continue
                
            # Check if hostile
            relation = self.game.faction_relations.get(self.faction_id, {}).get(ship.faction_id, 0.0)
            if relation >= -0.5:  # Not hostile enough
                continue
                
            distance = self.pos.distance_to(ship.pos)
            if distance < min_distance:
                min_distance = distance
                nearest_enemy = ship
                
        return nearest_enemy

    def load_ammo(self, ammo_id):
        """Load ammunition into inventory and compatible weapons."""
        ammo_template = get_outfit_by_id(ammo_id)
        if not ammo_template:
            self.game.set_status_message(f"Ammunition '{ammo_id}' not found.", (255, 100, 100))
            return False
        
        if not hasattr(ammo_template, 'category') or ammo_template.category != 'ammunition':
            self.game.set_status_message(f"'{ammo_id}' is not ammunition.", (255, 100, 100))
            return False

        # Store ammo in available inventory
        quantity = getattr(ammo_template, 'quantity', 10)
        if ammo_id in self.available_ammo_types:
            self.available_ammo_types[ammo_id].quantity += quantity
        else:
            self.available_ammo_types[ammo_id] = ammo_template.clone()
            self.available_ammo_types[ammo_id].quantity = quantity
        
        # Auto-load into compatible launchers that are empty
        loaded_count = 0
        compatible_launchers = getattr(ammo_template, 'compatible_launchers', [])
        
        for weapon in self.weapons:
            if (getattr(weapon, 'uses_ammo', False) and 
                weapon.id in compatible_launchers and
                getattr(weapon, 'current_ammo', 0) == 0):  # Only load if empty
                max_ammo = getattr(weapon, 'max_ammo', 0)
                ammo_to_load = min(quantity, max_ammo)
                weapon.current_ammo = ammo_to_load
                weapon.loaded_ammo_type = ammo_id  # Track what type is loaded
                loaded_count += ammo_to_load
                quantity -= ammo_to_load
                if quantity <= 0:
                    break
        
        # Update inventory
        self.available_ammo_types[ammo_id].quantity = quantity
        
        if loaded_count > 0:
            self.game.set_status_message(f"Loaded {loaded_count}x {ammo_template.name}.", (100, 255, 100))
            return True
        else:
            self.game.set_status_message(f"Added {ammo_template.quantity}x {ammo_template.name} to inventory.", (100, 255, 100))
            return True

    def _has_reverse_thrusters(self):
        """Check if the player has reverse thrusters equipped."""
        # Check for specific reverse thruster outfits
        reverse_thruster_outfits = ["reverse_thrusters", "maneuvering_thrusters", "advanced_engines"]

        for outfit_id in reverse_thruster_outfits:
            if outfit_id in self.installed_outfits and self.installed_outfits[outfit_id] > 0:
                return True

        return False

    def _recalculate_ship_stats(self):
        """Recalculate ship stats based on installed outfits."""
        # Reset to base values
        self.max_shields = self.base_max_shields
        self.shields = min(self.shields, self.max_shields)  # Don't exceed new max
        self.max_armor = self.base_max_armor
        self.armor = min(self.armor, self.max_armor)
        self.acceleration = self.base_acceleration
        self.turn_rate = self.base_turn_rate
        self.max_speed = self.base_max_speed

        # Clear weapons list
        self.weapons = []

        # Apply effects from all installed outfits
        for outfit_id, quantity in self.installed_outfits.items():
            outfit_template = get_outfit_by_id(outfit_id)
            if not outfit_template:
                continue

            for _ in range(quantity):
                outfit_instance = outfit_template.clone()

                if isinstance(outfit_instance, Weapon):
                    self.weapons.append(outfit_instance)
                elif isinstance(outfit_instance, DefenseOutfit):
                    outfit_instance.apply_effects(self)
                elif isinstance(outfit_instance, EngineOutfit):
                    outfit_instance.apply_effects(self)
                elif isinstance(outfit_instance, ElectronicsOutfit):
                    outfit_instance.apply_effects(self)
                elif isinstance(outfit_instance, UtilityOutfit):
                    outfit_instance.apply_effects(self)

        # Ensure values don't go below minimum thresholds
        self.acceleration = max(0.05, self.acceleration)
        self.turn_rate = max(0.05, self.turn_rate)
        self.max_speed = max(1.0, self.max_speed)

    def get_current_weapon(self):
        """Get the currently selected weapon."""
        if not self.weapons or self.active_weapon_index >= len(self.weapons):
            return None
        return self.weapons[self.active_weapon_index]
    
    def get_compatible_ammo_for_weapon(self, weapon):
        """Get list of compatible ammo types for a weapon."""
        if not getattr(weapon, 'uses_ammo', False):
            return []
        
        compatible_ammo = []
        for ammo_id, ammo_outfit in self.available_ammo_types.items():
            compatible_launchers = getattr(ammo_outfit, 'compatible_launchers', [])
            if weapon.id in compatible_launchers:
                compatible_ammo.append(ammo_id)
        return compatible_ammo
    
    def switch_launcher_ammo(self, weapon, new_ammo_id):
        """Switch the ammo type loaded in a launcher."""
        if not getattr(weapon, 'uses_ammo', False):
            return False
        
        # Check if we have this ammo type available
        if new_ammo_id not in self.available_ammo_types:
            return False
        
        # Unload current ammo back to inventory
        current_ammo_type = getattr(weapon, 'loaded_ammo_type', None)
        current_ammo_count = getattr(weapon, 'current_ammo', 0)
        
        if current_ammo_type and current_ammo_count > 0:
            if current_ammo_type in self.available_ammo_types:
                self.available_ammo_types[current_ammo_type].quantity += current_ammo_count
        
        # Load new ammo type
        max_ammo = getattr(weapon, 'max_ammo', 0)
        available_ammo = self.available_ammo_types[new_ammo_id].quantity
        ammo_to_load = min(available_ammo, max_ammo)
        
        weapon.current_ammo = ammo_to_load
        weapon.loaded_ammo_type = new_ammo_id
        self.available_ammo_types[new_ammo_id].quantity -= ammo_to_load
        
        return True

    def fire_weapon(self, dt):
        """Fire the currently active weapon if possible."""
        weapon = self.get_current_weapon()
        if not weapon:
            return None
        
        # Update weapon
        weapon.update(dt)
        
        if not weapon.can_fire():
            return None

        # Check power requirements
        power_cost = getattr(weapon, 'power_cost', 5.0)
        if not self.consume_power(power_cost):
            self.game.set_status_message("Insufficient power for weapon!", (255, 100, 100))
            return None

        # Get target for guided/turret weapons
        target = self.game.targeted_object

        # Auto-target for turrets if no target selected
        if weapon.mount_type == MOUNT_TYPE_TURRET and not target:
            target = self._find_nearest_enemy()
            # TEMP FIX: For testing, allow turrets to fire forward if no enemies found
            if not target:
                print(f"Turret {weapon.name} firing forward (no targets found)")
            
        # Check if we need a target for guided projectiles (based on projectile behavior, not mount type)
        if (hasattr(weapon, 'projectile_behavior') and weapon.projectile_behavior == 'guided' and 
            not getattr(weapon, 'uses_ammo', False) and not target):
            self.game.set_status_message(f"No target selected for guided {weapon.name}.", (255, 255, 0))
            return None

        # Calculate spawn position and angle
        spawn_pos = self.pos.copy()
        projectile_angle = self.angle

        # Calculate forward direction
        forward_angle = math.radians(self.angle - 90)
        forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))

        # Adjust spawn position and angle based on mount type
        if weapon.mount_type == MOUNT_TYPE_FIXED:
            offset_to_nose = self.image_orig.get_height() / 2
            spawn_pos = self.pos + forward * offset_to_nose
            projectile_angle = self.angle  # Fixed: use ship angle
        elif weapon.mount_type == MOUNT_TYPE_TURRET:
            if target:
                # Turret with target: aim at target
                to_target = target.pos - self.pos
                if to_target.length_squared() > 0:
                    # Calculate angle to target (convert from pygame coords to game coords)
                    target_angle = math.degrees(math.atan2(to_target.y, to_target.x)) + 90
                    projectile_angle = target_angle % 360
                    
                    # Spawn position offset toward target
                    to_target_normalized = to_target.normalize()
                    spawn_pos = self.pos + to_target_normalized * (self.rect.width / 3)
                else:
                    # Target is at same position, fire forward
                    offset_to_nose = self.image_orig.get_height() / 2
                    spawn_pos = self.pos + forward * offset_to_nose
                    projectile_angle = self.angle
            else:
                # Turret without target: fire forward like fixed weapon
                offset_to_nose = self.image_orig.get_height() / 2
                spawn_pos = self.pos + forward * offset_to_nose
                projectile_angle = self.angle


        # Apply accuracy and spread
        if hasattr(weapon, 'accuracy') and weapon.accuracy < 1.0:
            accuracy_variation = (1.0 - weapon.accuracy) * 20
            projectile_angle += random.uniform(-accuracy_variation, accuracy_variation)

        if hasattr(weapon, 'spread') and weapon.spread > 0:
            projectile_angle += random.uniform(-weapon.spread, weapon.spread)

        # CRITICAL FIX: Separate launcher vs direct fire weapon logic
        if getattr(weapon, 'uses_ammo', False):
            # LAUNCHER - uses ammo specifications for damage and behavior
            current_ammo_type = getattr(weapon, 'loaded_ammo_type', None)
            if not current_ammo_type or current_ammo_type not in self.available_ammo_types:
                self.game.set_status_message(f"No ammo loaded in {weapon.name}!", (255, 100, 100))
                return None
                
            # Get ammo data for projectile creation
            ammo_data = self.available_ammo_types[current_ammo_type]
            
            # Use AMMO specifications for projectile
            ammo_shield_damage = getattr(ammo_data, 'shield_damage', 50)
            ammo_armor_damage = getattr(ammo_data, 'armor_damage', 50)
            ammo_speed = getattr(ammo_data, 'projectile_speed', 300)
            ammo_range = getattr(ammo_data, 'range', weapon.range)  # Use ammo range, fallback to weapon range
            ammo_behavior = getattr(ammo_data, 'projectile_behavior', 'dumbfire')
            ammo_tracking = getattr(ammo_data, 'tracking_strength', 0.0)
            ammo_sprite = getattr(ammo_data, 'projectile_sprite', '')
            
            # Handle targeting for guided missiles
            missile_target = target if (ammo_behavior == 'guided' and target and ammo_tracking > 0) else None
            
            projectile = MissileProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                ammo_shield_damage,
                ammo_armor_damage,
                ammo_range,  # FIXED: Use ammo range instead of weapon range
                missile_target,
                tracking=ammo_tracking,
                delay_time=getattr(ammo_data, 'delay_time', 0.0),
                projectile_speed=ammo_speed,
                image_path=ammo_sprite
            )
        else:
            # DIRECT FIRE WEAPON - uses weapon specifications for damage
            # Extract visual properties from weapon
            weapon_color = getattr(weapon, 'beam_color', (255, 0, 0))
            weapon_size = getattr(weapon, 'beam_size', (8, 2))
            
            # Convert lists to tuples for pygame compatibility
            if isinstance(weapon_color, list):
                weapon_color = tuple(weapon_color)
            if isinstance(weapon_size, list):
                weapon_size = tuple(weapon_size)
                
            is_beam_weapon = weapon.projectile_behavior == BEHAVIOR_BEAM
            
            if weapon.projectile_behavior == BEHAVIOR_INSTANT:
                # Laser-type weapons
                projectile = LaserProjectile(
                    self.game,
                    self,
                    spawn_pos,
                    projectile_angle,
                    weapon.shield_damage,
                    weapon.armor_damage,
                    weapon.range,
                    color=weapon_color,
                    beam=False
                )
                # Apply custom size after creation
                projectile.size = weapon_size
                projectile.create_default_image()
            elif weapon.projectile_behavior == BEHAVIOR_BEAM:
                # Beam weapons - create instant line from ship front to target/max range
                # Calculate proper start position (front of ship)
                forward_angle = math.radians(projectile_angle - 90)
                forward_dir = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))
                beam_start = self.pos + forward_dir * (self.image_orig.get_height() / 2)
                
                # Create beam with proper positioning
                projectile = LaserProjectile(
                    self.game,
                    self,
                    beam_start,  # Start from front of ship
                    projectile_angle,
                    weapon.shield_damage,
                    weapon.armor_damage,
                    weapon.range,
                    color=weapon_color,
                    beam=True
                )
                # Override the size and positioning for beams
                projectile.size = (weapon.range, weapon_size[1])  # Full range length
                projectile.lifetime = 0.05  # Very short flash
                projectile.speed = 0  # Stationary
                projectile.create_default_image()
                
                # Position beam correctly along the firing direction
                beam_end = beam_start + forward_dir * weapon.range
                beam_center = (beam_start + beam_end) / 2
                projectile.pos = beam_center
                projectile.rect.center = beam_center
            else:
                # Default projectile
                projectile = LaserProjectile(
                    self.game,
                    self,
                    spawn_pos,
                    projectile_angle,
                    weapon.shield_damage,
                    weapon.armor_damage,
                    weapon.range,
                    color=weapon_color,
                    beam=False
                )
                # Apply custom size after creation
                projectile.size = weapon_size
                projectile.create_default_image()

        # Fire the weapon
        if weapon.fire():
            self.game.all_sprites.add(projectile)
            self.projectiles.add(projectile)

            # Trigger weapon firing effects
            self._trigger_weapon_effects(weapon, projectile)

            # Play weapon sound effect
            if hasattr(weapon, 'fire_sound') and weapon.fire_sound:
                self.game.sound_manager.play_weapon_sound(weapon.fire_sound)

            return projectile

        return None

    def _trigger_weapon_effects(self, weapon, projectile):
        """Trigger animation effects for weapon firing."""
        print(f"DEBUG: Triggering weapon effects for {weapon.name}")
        
        if hasattr(self.game, 'animation_integration') and self.game.animation_integration:
            # Set weapon properties needed for animation lookup
            weapon.outfit_id = weapon.id
            weapon.category = 'weapons'
            
            print(f"DEBUG: Animation integration found, triggering muzzle flash for {weapon.id}")
            
            # Trigger muzzle flash effect
            from game_objects.animation_integration import trigger_weapon_fire
            trigger_weapon_fire(self.game.animation_integration, weapon)

            # Add weapon source reference to projectile for impact effects
            if projectile:
                projectile.weapon_source = weapon
        else:
            print("DEBUG: No animation integration found!")

    def _trigger_engine_trail_effects(self):
        """Trigger engine trail effects when thrusting."""
        print("DEBUG: Triggering engine trail effects")
        
        if hasattr(self.game, 'animation_integration') and self.game.animation_integration:
            # Set ship properties needed for animation lookup  
            # Use the ship_id from player creation, not from ship object
            if hasattr(self, '_ship_id'):
                self.ship_id = self._ship_id
            elif hasattr(self.ship, 'id'):
                self.ship_id = self.ship.id
            else:
                # Fallback: try to determine ship_id from ship name/type
                self.ship_id = getattr(self.ship, 'ship_type', getattr(self.ship, 'name', 'scout'))
            
            self.id = self.ship_id
            self.category = 'ships'
            
            print(f"DEBUG: Animation integration found, triggering engine trail for ship {self.ship_id}")
            
            # Trigger engine trail effect
            from game_objects.animation_integration import trigger_ship_engine_trail
            trigger_ship_engine_trail(self.game.animation_integration, self)
        else:
            print("DEBUG: No animation integration found for engine trails!")
    
    def _stop_engine_trail_effects(self):
        """Stop engine trail effects when not thrusting."""
        if hasattr(self.game, 'animation_integration') and self.game.animation_integration:
            # Set ship properties needed for animation lookup
            # Use the ship_id from player creation, not from ship object
            if hasattr(self, '_ship_id'):
                self.ship_id = self._ship_id
            elif hasattr(self.ship, 'id'):
                self.ship_id = self.ship.id
            else:
                # Fallback: try to determine ship_id from ship name/type
                self.ship_id = getattr(self.ship, 'ship_type', getattr(self.ship, 'name', 'scout'))
            
            self.id = self.ship_id
            self.category = 'ships'
            
            # Stop engine trail effect
            from game_objects.animation_integration import trigger_ship_engine_trail
            trigger_ship_engine_trail(self.game.animation_integration, self, enable=False)

    def cycle_weapons(self):
        """Cycle to the next available weapon (R key)."""
        if not self.weapons:
            self.game.set_status_message("No weapons available", (255, 100, 100))
            return

        self.active_weapon_index = (self.active_weapon_index + 1) % len(self.weapons)
        weapon = self.get_current_weapon()
        
        if weapon:
            self.game.set_status_message(f"Selected: {weapon.name}", (0, 255, 0))
    
    def cycle_ammo_for_current_weapon(self):
        """Cycle through ammo types for the currently selected launcher (T key)."""
        weapon = self.get_current_weapon()
        if not weapon or not getattr(weapon, 'uses_ammo', False):
            self.game.set_status_message("Current weapon doesn't use ammo", (255, 255, 100))
            return
        
        # Find all compatible ammo types for this weapon
        compatible_ammo = self.get_compatible_ammo_for_weapon(weapon)
        
        if len(compatible_ammo) <= 1:
            self.game.set_status_message("No other ammo types available", (255, 255, 100))
            return
        
        # Find current ammo index and cycle to next
        current_ammo_type = getattr(weapon, 'loaded_ammo_type', None)
        try:
            current_index = compatible_ammo.index(current_ammo_type) if current_ammo_type else 0
            next_index = (current_index + 1) % len(compatible_ammo)
            next_ammo_id = compatible_ammo[next_index]
        except ValueError:
            next_ammo_id = compatible_ammo[0]
        
        # Switch to the new ammo type
        if self.switch_launcher_ammo(weapon, next_ammo_id):
            ammo_name = self.available_ammo_types[next_ammo_id].name
            self.game.set_status_message(f"Switched to: {ammo_name}", (0, 255, 0))
        else:
            self.game.set_status_message(f"Cannot switch to {next_ammo_id} ammo", (255, 100, 100))

    def get_active_weapon_info(self):
        """Get information about the currently active weapon."""
        weapon = self.get_current_weapon()
        if not weapon:
            return "No weapons equipped"

        if getattr(weapon, 'uses_ammo', False):
            # Launcher - show loaded ammo type
            loaded_ammo_type = getattr(weapon, 'loaded_ammo_type', None)
            if loaded_ammo_type and loaded_ammo_type in self.available_ammo_types:
                ammo_name = self.available_ammo_types[loaded_ammo_type].name
                current_ammo = getattr(weapon, 'current_ammo', 0)
                max_ammo = getattr(weapon, 'max_ammo', 0)
                return f"{weapon.name}: {ammo_name} ({current_ammo}/{max_ammo})"
            else:
                return f"{weapon.name}: No ammo loaded"
        else:
            # Direct fire weapon
            return weapon.name

    def update(self, dt=1/60):
        """Update player state."""
        # Prepare input state for rules engine
        keystate = pg.key.get_pressed()
        input_state = {
            'thrust_forward': keystate[pg.K_w],
            'thrust_reverse': keystate[pg.K_s],
            'turn_left': keystate[pg.K_a],
            'turn_right': keystate[pg.K_d],
            'afterburner': keystate[pg.K_LSHIFT],  # Optional afterburner key
            'fire_weapons': keystate[pg.K_SPACE]
        }

        # Get nearby objects for rules engine
        nearby_objects = []
        if hasattr(self.game, 'all_sprites'):
            for sprite in self.game.all_sprites:
                if sprite != self:
                    nearby_objects.append(sprite)

        # Apply outfit rules engine - this handles defense, engine, and electronics systems
        try:
            update_all_outfit_systems(self, dt, input_state, nearby_objects)
        except Exception as e:
            print(f"Error in outfit rules engine: {e}")

        acc = pg.math.Vector2(0, 0)

        # Handle rotation
        if keystate[pg.K_d]:
            # NEW: Check power for turning
            turn_power_cost = 1.0 * dt  # Small power cost for turning
            if self.consume_power(turn_power_cost):
                self.angle += self.turn_rate
                if self.angle >= 360:
                    self.angle -= 360
            elif self.power > 0:  # Minimal turning with remaining power
                self.angle += self.turn_rate * 0.1
                if self.angle >= 360:
                    self.angle -= 360
            # No turning at all if power is 0

        if keystate[pg.K_a]:
            # NEW: Check power for turning
            turn_power_cost = 1.0 * dt  # Small power cost for turning
            if self.consume_power(turn_power_cost):
                self.angle -= self.turn_rate
                if self.angle < 0:
                    self.angle += 360
            elif self.power > 0:  # Minimal turning with remaining power
                self.angle -= self.turn_rate * 0.1
                if self.angle < 0:
                    self.angle += 360
            # No turning at all if power is 0

        # Update ship image
        self.rotate()

        # Calculate forward direction
        forward_angle = math.radians(self.angle - 90)
        forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))

        # Apply thrust - IMPROVED PHYSICS-BASED SYSTEM
        thrust_strength = self.acceleration
        thrust_power_used = False

        # Check if player has reverse thrusters outfit installed
        has_reverse_thrusters = self._has_reverse_thrusters()

        # NEW: Complete power blackout system
        if self.power <= 0:
            # COMPLETE BLACKOUT - no movement at all
            acc = pg.math.Vector2(0, 0)
            # Stop any engine trail effects
            self._stop_engine_trail_effects()
            if random.random() < 0.05:  # Occasional blackout message
                self.game.set_status_message("POWER FAILURE - All systems offline!", RED)
        else:
            # Normal power operations - FORWARD THRUST ONLY (unless reverse thrusters equipped)
            if keystate[pg.K_w]:
                # Check power for forward thrust
                power_cost = 10.0 * dt  # Base thruster power cost
                if self.consume_power(power_cost):
                    acc = forward * thrust_strength
                    thrust_power_used = True
                    # Trigger engine trail effects
                    self._trigger_engine_trail_effects()
                else:
                    # Very reduced thrust when low on power
                    acc = forward * thrust_strength * 0.1
                    if random.random() < 0.1:
                        self.game.set_status_message("Critical power - minimal thrust!", RED)
            else:
                # Not thrusting - stop engine trail effects
                self._stop_engine_trail_effects()

            # S key behavior depends on whether reverse thrusters are equipped
            if keystate[pg.K_s]:
                if has_reverse_thrusters:
                    # Equipped with reverse thrusters - direct reverse thrust
                    power_cost = 6.0 * dt  # Slightly less efficient
                    if self.consume_power(power_cost):
                        acc = -forward * thrust_strength * 0.6  # 60% reverse power
                        thrust_power_used = True
                        if random.random() < 0.02:  # Occasional message
                            self.game.set_status_message("Reverse thrusters engaged", GREEN)
                    else:
                        # Very minimal reverse thrust when low on power
                        acc = -forward * thrust_strength * 0.05
                else:
                    # No reverse thrusters - show helpful message occasionally
                    if random.random() < 0.02:  # Occasional reminder
                        self.game.set_status_message("No reverse thrusters - turn around to slow down!", YELLOW)

        # Check for weapon firing
        if keystate[pg.K_SPACE]:
            # NEW: Weapons completely disabled without power
            if self.power <= 0:
                if random.random() < 0.1:  # Occasional message
                    self.game.set_status_message("No power for weapons!", RED)
            else:
                self.fire_weapon(dt)

        # Apply acceleration
        self.vel += acc

        # Apply drag
        if self.vel.length_squared() > 0:
            drag = self.vel.normalize() * DRAG_FACTOR * self.vel.length_squared()
            if drag.length_squared() < self.vel.length_squared():
                self.vel -= drag
            else:
                self.vel = pg.math.Vector2(0, 0)

        # Limit speed
        if self.vel.length_squared() > self.max_speed * self.max_speed:
            self.vel.scale_to_length(self.max_speed)

        # Update position
        self.pos += self.vel
        self.rect.center = self.pos

        # World boundary checks
        ship_width = self.rect.width
        ship_height = self.rect.height

        if self.pos.x > self.game.camera.width - ship_width / 2:
            self.pos.x = self.game.camera.width - ship_width / 2
            self.vel.x *= -0.5
        if self.pos.x < ship_width / 2:
            self.pos.x = ship_width / 2
            self.vel.x *= -0.5
        if self.pos.y > self.game.camera.height - ship_height / 2:
            self.pos.y = self.game.camera.height - ship_height / 2
            self.vel.y *= -0.5
        if self.pos.y < ship_height / 2:
            self.pos.y = ship_height / 2
            self.vel.y *= -0.5
        self.rect.center = self.pos

        # Update all weapons
        for weapon in self.weapons:
            weapon.update(dt)

        # Shield regeneration
        if self.shield_recharge_timer > 0:
            self.shield_recharge_timer -= 1
        elif self.shields < self.max_shields:
            # NEW: Check power for shield regeneration
            shield_power_cost = 2.0 * dt  # Base shield regen power cost
            if self.consume_power(shield_power_cost):
                self.shields = min(self.max_shields, self.shields + 0.1)
            # If no power, shields don't regenerate

        # NEW: Power regeneration
        self.regenerate_power(dt)

        # Update current power/fuel from ship (in case modified by outfits)
        # Note: power and fuel are now managed by the player directly

    def rotate(self):
        """Update the ship's image based on its current angle."""
        old_center = self.rect.center

        try:
            if self.spritesheet and self.spritesheet.image:
                frame = self.spritesheet.get_frame_by_angle(self.angle)
                if frame:
                    self.image = frame
                else:
                    self.image = pg.transform.rotate(self.image_orig, -self.angle)
            else:
                self.image = pg.transform.rotate(self.image_orig, -self.angle)
        except Exception as e:
            print(f"Error during rotation: {e}")
            self.image = self.image_orig.copy()

        self.rect = self.image.get_rect()
        self.rect.center = old_center

    def take_damage(self, amount, attacker_faction_id=None):
        """Take damage from an attack."""
        if self.shields > 0:
            self.shields -= amount
            if self.shields < 0:
                self.armor += self.shields
                self.shields = 0
        else:
            self.armor -= amount

        # Reset shield recharge timer
        self.shield_recharge_timer = self.shield_recharge_delay

        # Update damage state effects
        self._update_damage_effects()

        # Check if player is destroyed
        if self.armor <= 0:
            self.game.set_status_message("Your ship has been destroyed!", (255, 0, 0))
            print("Player ship destroyed!")
            self.game.playing = False
            self.game.state = "GAME_OVER"
        # Check if player is disabled (shields at 0 and armor at 15% or less)
        elif self.shields <= 0 and self.armor <= self.max_armor * 0.15:
            if not getattr(self, 'is_disabled', False):
                self.is_disabled = True
                self.game.set_status_message("Your ship is disabled! Systems failing!", (255, 255, 0))
                print(f"Player ship disabled! Shields: {self.shields:.1f}, Armor: {self.armor:.1f}/{self.max_armor}")
        else:
            # Player recovered from disabled state
            if getattr(self, 'is_disabled', False):
                self.is_disabled = False
                self.game.set_status_message("Ship systems restored!", (0, 255, 0))

    def _update_damage_effects(self):
        """Update damage state effects for the player ship."""
        print(f"DEBUG: Updating damage effects for player ship")
        
        if hasattr(self.game, 'animation_integration') and self.game.animation_integration:
            # Set ship properties needed for animation lookup
            # Use the ship_id from player creation, not from ship object
            if hasattr(self, '_ship_id'):
                self.ship_id = self._ship_id
            elif hasattr(self.ship, 'id'):
                self.ship_id = self.ship.id
            else:
                # Fallback: try to determine ship_id from ship name/type
                self.ship_id = getattr(self.ship, 'ship_type', getattr(self.ship, 'name', 'scout'))
            
            self.id = self.ship_id
            self.category = 'ships'
            
            print(f"DEBUG: Animation integration found, updating damage state for ship {self.ship_id}")
            
            # Update damage effects
            from game_objects.animation_integration import trigger_ship_damage_effects
            trigger_ship_damage_effects(self.game.animation_integration, self)
        else:
            print("DEBUG: No animation integration found for damage effects!")
