#!/usr/bin/env python3
"""
Verification script for editor fixes
"""

import os
import sys

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_position_handling():
    """Test that position types are properly handled."""
    print("Testing position type handling...")
    
    # Test the position calculator with list input (simulating the bug)
    from game_objects.position_calculator import PositionCalculator
    import pygame as pg
    
    # Create a mock entity
    class MockEntity:
        def __init__(self):
            self.pos = pg.math.Vector2(100, 100)
            self.angle = 0
    
    entity = MockEntity()
    
    # Test string position (should work)
    try:
        pos1 = PositionCalculator.calculate_position(entity, "entity_center", (0, 0))
        print("✓ String position type works")
    except Exception as e:
        print(f"✗ String position type failed: {e}")
    
    # Test with position normalization (simulating the fix)
    test_positions = ["ship_center", ["ship_center"], 0, None]
    for test_pos in test_positions:
        try:
            # Simulate the fix in animation_endpoint_manager
            position_type = test_pos
            if isinstance(position_type, list):
                position_type = position_type[0] if position_type else 'entity_center'
            elif not isinstance(position_type, str):
                position_type = str(position_type) if position_type else 'entity_center'
            
            pos = PositionCalculator.calculate_position(entity, position_type, (0, 0))
            print(f"✓ Position {test_pos} -> {position_type} works")
        except Exception as e:
            print(f"✗ Position {test_pos} failed: {e}")

def test_animation_endpoints_saving():
    """Test that animation endpoints are properly saved."""
    print("\nTesting animation endpoint saving...")
    
    # Test the animation endpoint UI save/load
    try:
        # Create a test ammunition object
        class TestAmmo:
            def __init__(self):
                self.name = "Test Missile"
                self.animation_endpoints = {
                    "trail_effect": {
                        "effect": "engine_trail",
                        "position": "projectile_rear",
                        "duration": 1.0,
                        "scale": 0.8,
                        "continuous": True,
                        "offset": [0, -10]
                    }
                }
        
        ammo = TestAmmo()
        
        # Test that animation endpoints exist
        if hasattr(ammo, 'animation_endpoints'):
            print(f"✓ Animation endpoints attribute exists: {ammo.animation_endpoints}")
        else:
            print("✗ Animation endpoints attribute missing")
            
        # Test position type handling in the endpoint
        endpoint_data = ammo.animation_endpoints["trail_effect"]
        position = endpoint_data.get("position", "impact_point")
        if isinstance(position, list):
            position = position[0] if position else 'impact_point'
        position = str(position)
        
        print(f"✓ Position type normalized: {position}")
        
    except Exception as e:
        print(f"✗ Animation endpoint test failed: {e}")

def test_multiselect_handling():
    """Test multiselect value handling."""
    print("\nTesting multiselect value handling...")
    
    # Test various input types for compatible launchers
    test_values = [
        ["misslerack", "torpedolauncher"],  # List (correct)
        "misslerack",  # Single string
        [],  # Empty list
        "",  # Empty string
        None  # None value
    ]
    
    for test_value in test_values:
        try:
            # Simulate the fix in ParameterLoader._load_multi_select
            values_to_select = []
            if isinstance(test_value, list):
                values_to_select = test_value
            elif isinstance(test_value, str) and test_value.strip():
                values_to_select = [test_value]
            elif test_value:
                values_to_select = [str(test_value)]
            
            print(f"✓ Input {test_value} -> {values_to_select}")
            
        except Exception as e:
            print(f"✗ Multiselect test failed for {test_value}: {e}")

def main():
    """Run all verification tests."""
    print("=" * 60)
    print("Editor Fixes Verification")
    print("=" * 60)
    
    # Initialize pygame for testing
    import pygame as pg
    pg.init()
    
    test_position_handling()
    test_animation_endpoints_saving()
    test_multiselect_handling()
    
    print("\n" + "=" * 60)
    print("Verification complete!")
    print("If all tests show ✓, the fixes should resolve the editor issues.")
    print("=" * 60)

if __name__ == "__main__":
    main()
