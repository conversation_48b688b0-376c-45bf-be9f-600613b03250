"""
Ship Editor for the Enhanced Content Editor
Handles editing of ships
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json
from .outfit_editors.ui_components import StandardParameterGrid, ParameterLoader
from .outfit_editors.animation_endpoint_ui import ShipAnimationEndpoints

class ShipEditor:
    """Editor for ships."""

    def __init__(self, parent, data_manager):
        self.parent = parent
        self.data_manager = data_manager
        self.current_ship = None

        # Create main frame
        self.frame = ttk.Frame(parent)

        # Setup UI
        self.setup_ui()

    def setup_ui(self):
        """Setup the ship editor UI."""
        # Create horizontal layout
        paned = ttk.PanedWindow(self.frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Ship list
        left_frame = ttk.LabelFrame(paned, text="Ships Library")
        paned.add(left_frame, weight=1)

        # Ship listbox
        self.listbox = tk.Listbox(left_frame)
        self.listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.listbox.bind("<<ListboxSelect>>", self.on_ship_select)

        # Buttons for ship management
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(button_frame, text="New Ship", command=self.create_new_ship).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Delete", command=self.delete_ship).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Export", command=self.export_ships).pack(side=tk.LEFT, padx=2)

        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text="Ship Editor")
        paned.add(right_frame, weight=2)

        self.setup_editor_ui(right_frame)

    def setup_editor_ui(self, parent):
        """Setup the ship editor interface."""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties Grid
        self.basic_grid = StandardParameterGrid(scrollable_frame, "Basic Properties")
        self.basic_grid.add_string_field("Name", "name")
        self.basic_grid.add_int_field("Cost", "cost", 1000, 10000000)
        self.basic_grid.add_combo_field("Ship Class", "ship_class", ["fighter", "freighter", "transport", "corvette", "frigate", "cruiser", "destroyer", "carrier", "battleship", "dreadnought"])
        self.basic_grid.add_combo_field("Size", "size", ["small", "medium", "large", "capital"])
        self.basic_grid.add_int_field("Min Tech Level", "min_tech_level", 1, 10)
        self.basic_grid.pack(fill=tk.X, padx=5, pady=5)

        # Physical Properties Grid
        self.physical_grid = StandardParameterGrid(scrollable_frame, "Physical Properties")
        self.physical_grid.add_int_field("Outfit Space", "outfit_space", 1, 500)
        self.physical_grid.add_int_field("Cargo Space", "cargo_space", 0, 1000)
        self.physical_grid.add_float_field("Mass", "mass", 0.1, 50.0, 0.1)
        self.physical_grid.pack(fill=tk.X, padx=5, pady=5)

        # Performance Properties Grid
        self.performance_grid = StandardParameterGrid(scrollable_frame, "Performance Properties")
        self.performance_grid.add_float_field("Max Speed", "max_speed", 0.5, 15.0, 0.1)
        self.performance_grid.add_float_field("Acceleration", "acceleration", 0.05, 3.0, 0.05)
        self.performance_grid.add_float_field("Turn Rate", "turn_rate", 0.05, 5.0, 0.05)
        self.performance_grid.pack(fill=tk.X, padx=5, pady=5)

        # Defense Properties Grid
        self.defense_grid = StandardParameterGrid(scrollable_frame, "Defense Properties")
        self.defense_grid.add_int_field("Shields", "shields", 0, 2000)
        self.defense_grid.add_int_field("Max Shields", "max_shields", 0, 2000)
        self.defense_grid.add_int_field("Armor", "armor", 0, 2000)
        self.defense_grid.add_int_field("Max Armor", "max_armor", 0, 2000)
        self.defense_grid.add_float_field("Shield Recharge Rate", "shield_recharge_rate", 0.0, 50.0, 0.1)
        self.defense_grid.pack(fill=tk.X, padx=5, pady=5)

        # Power System Properties Grid
        self.power_grid = StandardParameterGrid(scrollable_frame, "Power System Properties")
        self.power_grid.add_int_field("Power Capacity", "power_capacity", 50, 1000)
        self.power_grid.add_float_field("Power Regen Rate", "power_regen_rate", 1.0, 20.0, 0.1)
        self.power_grid.add_float_field("Thruster Power Cost", "thruster_power_cost", 1.0, 50.0, 0.1)
        self.power_grid.add_float_field("Weapon Power Cost Base", "weapon_power_cost_base", 1.0, 100.0, 0.1)
        self.power_grid.add_float_field("Shield Regen Power Cost", "shield_regen_power_cost", 0.5, 20.0, 0.1)
        self.power_grid.pack(fill=tk.X, padx=5, pady=5)

        # Fuel System Properties Grid
        self.fuel_grid = StandardParameterGrid(scrollable_frame, "Fuel System Properties")
        self.fuel_grid.add_int_field("Fuel Capacity", "fuel_capacity", 50, 1000)
        self.fuel_grid.add_float_field("Fuel Consumption Rate", "fuel_consumption_rate", 0.1, 10.0, 0.1)
        self.fuel_grid.add_float_field("Jump Fuel Cost", "jump_fuel_cost", 5.0, 100.0, 1.0)
        self.fuel_grid.pack(fill=tk.X, padx=5, pady=5)

        # Visual Properties Grid
        self.visual_grid = StandardParameterGrid(scrollable_frame, "Visual Properties")
        self.visual_grid.add_file_field("Shipyard Image", "shipyard_image", [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")])
        self.visual_grid.add_file_field("Shipyard Display Image", "shipyard_display_image", [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")])
        self.visual_grid.add_file_field("Game Sprite", "sprite", [("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")])
        self.visual_grid.add_int_field("Sprite Size", "sprite_size", 16, 512)
        self.visual_grid.add_int_field("Animation Frames", "animation_frames", 1, 64)
        self.visual_grid.pack(fill=tk.X, padx=5, pady=5)

        # Manufacturer Properties Grid
        self.manufacturer_grid = StandardParameterGrid(scrollable_frame, "Manufacturer Properties")
        self.manufacturer_grid.add_string_field("Manufacturer", "manufacturer")
        self.manufacturer_grid.add_string_field("Origin", "origin")
        self.manufacturer_grid.add_combo_field("Availability", "availability", ["common", "uncommon", "rare", "unique", "military", "civilian"])
        self.manufacturer_grid.pack(fill=tk.X, padx=5, pady=5)

        # Equipment Properties - Simple Dropdowns
        equipment_frame = ttk.LabelFrame(scrollable_frame, text="Default Equipment")
        equipment_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Get available outfits from registry
        try:
            import sys, os
            game_src_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "src")
            if game_src_path not in sys.path:
                sys.path.insert(0, game_src_path)
            from game_objects.standardized_outfits import OUTFITS_REGISTRY
            
            weapons = [""] + [f"{oid}" for oid, o in OUTFITS_REGISTRY.items() if o.category == "weapons"]
            defense = [""] + [f"{oid}" for oid, o in OUTFITS_REGISTRY.items() if o.category == "defense"]
            ammo = [""] + [f"{oid}" for oid, o in OUTFITS_REGISTRY.items() if o.category == "ammunition"]
        except Exception as e:
            weapons = defense = ammo = [""]
            print(f"Could not load outfits: {e}")
        
        # Create outfit dropdown variables
        self.outfit_vars = {}
        
        # Primary Weapon
        ttk.Label(equipment_frame, text="Primary Weapon:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.outfit_vars['weapon1'] = tk.StringVar()
        ttk.Combobox(equipment_frame, textvariable=self.outfit_vars['weapon1'], values=weapons, width=25).grid(row=0, column=1, padx=5, pady=2)
        
        # Secondary Weapon
        ttk.Label(equipment_frame, text="Secondary Weapon:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.outfit_vars['weapon2'] = tk.StringVar()
        ttk.Combobox(equipment_frame, textvariable=self.outfit_vars['weapon2'], values=weapons, width=25).grid(row=1, column=1, padx=5, pady=2)
        
        # Defense
        ttk.Label(equipment_frame, text="Defense:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.outfit_vars['defense'] = tk.StringVar()
        ttk.Combobox(equipment_frame, textvariable=self.outfit_vars['defense'], values=defense, width=25).grid(row=2, column=1, padx=5, pady=2)
        
        # Ammo
        ttk.Label(equipment_frame, text="Ammo:").grid(row=3, column=0, sticky="w", padx=5, pady=2)
        self.outfit_vars['ammo'] = tk.StringVar()
        ttk.Combobox(equipment_frame, textvariable=self.outfit_vars['ammo'], values=ammo, width=25).grid(row=3, column=1, padx=5, pady=2)
        
        # Animation Endpoints
        self.animation_endpoints = ShipAnimationEndpoints(scrollable_frame)
        self.animation_endpoints.pack(fill=tk.X, padx=5, pady=5)
        
        # Description
        desc_frame = ttk.LabelFrame(scrollable_frame, text="Description")
        desc_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.description_text = tk.Text(desc_frame, height=4, wrap=tk.WORD)
        self.description_text.pack(fill=tk.X, padx=5, pady=5)

        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)
        ttk.Button(save_frame, text="Save Ship", command=self.save_ship).pack(side=tk.RIGHT, padx=5)

    def load_data(self):
        """Load ships into the listbox."""
        if hasattr(self, 'listbox'):
            self.listbox.delete(0, tk.END)

            ships = self.data_manager.get_all_ships()
            for ship_id, ship in ships.items():
                ship_class = getattr(ship, 'ship_class', 'unknown')
                size = getattr(ship, 'size', 'unknown')
                display_name = f"{ship.name} ({ship_class}, {size})"
                self.listbox.insert(tk.END, display_name)

    def on_ship_select(self, event=None):
        """Handle ship selection from list."""
        selection = self.listbox.curselection()
        if not selection:
            return

        # Get selected ship
        ships = list(self.data_manager.get_all_ships().values())

        if selection[0] < len(ships):
            ship = ships[selection[0]]
            self.load_ship_into_editor(ship)

    def load_ship_into_editor(self, ship):
        """Load ship data into the editor."""
        self.current_ship = ship
        
        # Load using standardized parameter loader
        ParameterLoader.load_outfit_parameters(ship, self.basic_grid)
        ParameterLoader.load_outfit_parameters(ship, self.physical_grid)
        ParameterLoader.load_outfit_parameters(ship, self.performance_grid)
        ParameterLoader.load_outfit_parameters(ship, self.defense_grid)
        ParameterLoader.load_outfit_parameters(ship, self.power_grid)
        ParameterLoader.load_outfit_parameters(ship, self.fuel_grid)
        ParameterLoader.load_outfit_parameters(ship, self.visual_grid)
        ParameterLoader.load_outfit_parameters(ship, self.manufacturer_grid)
        
        # Update visual properties with actual sprite info from the game's sprite system
        try:
            # Try to get sprite information from the game's sprite mapper
            import sys, os
            game_src_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "src")
            if game_src_path not in sys.path:
                sys.path.insert(0, game_src_path)
            
            from game_objects.ship_sprite_mapper import SHIP_SPRITE_MAPPER
            sprite_metadata = SHIP_SPRITE_MAPPER.get_sprite_metadata_for_ship(ship.id, ship.size)
            
            # Update visual grid with actual sprite information
            if sprite_metadata['game_sprite']:
                self.visual_grid.vars['sprite'].set(sprite_metadata['game_sprite'])
            if 'sprite_size' in sprite_metadata:
                self.visual_grid.vars['sprite_size'].set(sprite_metadata['sprite_size'])
            if 'animation_frames' in sprite_metadata:
                self.visual_grid.vars['animation_frames'].set(sprite_metadata['animation_frames'])
                
        except Exception as e:
            print(f"Could not load sprite mapper info: {e}")
        
        # Load default outfits into dropdowns BY CATEGORY (not position!)
        default_outfits = getattr(ship, 'default_outfits', {})
        
        # Clear dropdowns
        for var in self.outfit_vars.values():
            var.set("")
        
        # Load outfits from ship data BY CATEGORY - CRITICAL FIX!
        try:
            from game_objects.standardized_outfits import OUTFITS_REGISTRY
            
            weapons_found = []
            defense_found = []
            ammo_found = []
            
            # Categorize outfits properly
            for outfit_id in default_outfits.keys():
                if outfit_id in OUTFITS_REGISTRY:
                    outfit = OUTFITS_REGISTRY[outfit_id]
                    category = getattr(outfit, 'category', 'unknown')
                    
                    if category == 'weapons':
                        weapons_found.append(outfit_id)
                    elif category == 'defense':
                        defense_found.append(outfit_id)
                    elif category == 'ammunition':
                        ammo_found.append(outfit_id)
                        
            # Set dropdowns by category, not position
            if len(weapons_found) > 0: 
                self.outfit_vars['weapon1'].set(weapons_found[0])
            if len(weapons_found) > 1: 
                self.outfit_vars['weapon2'].set(weapons_found[1])
            if len(defense_found) > 0: 
                self.outfit_vars['defense'].set(defense_found[0])
            if len(ammo_found) > 0: 
                self.outfit_vars['ammo'].set(ammo_found[0])
                
            print(f"Ship {ship.id} outfits loaded by category:")
            print(f"  Weapons: {weapons_found}")
            print(f"  Defense: {defense_found}")
            print(f"  Ammo: {ammo_found}")
                
        except Exception as e:
            print(f"Error categorizing outfits: {e}")
            # Fallback to old position-based loading
            outfit_keys = list(default_outfits.keys())
            if len(outfit_keys) > 0: self.outfit_vars['weapon1'].set(outfit_keys[0])
            if len(outfit_keys) > 1: self.outfit_vars['weapon2'].set(outfit_keys[1])
            if len(outfit_keys) > 2: self.outfit_vars['defense'].set(outfit_keys[2])
            if len(outfit_keys) > 3: self.outfit_vars['ammo'].set(outfit_keys[3])
        
        # Load animation endpoints
        animation_endpoints = getattr(ship, 'animation_endpoints', {})
        self.animation_endpoints.load_endpoints(animation_endpoints)
        
        # Load description
        self.description_text.delete(1.0, tk.END)
        self.description_text.insert(1.0, getattr(ship, 'description', ''))

    def save_ship(self):
        """Save the current ship with editor values."""
        if not self.current_ship:
            messagebox.showwarning("Warning", "No ship selected to save")
            return

        try:
            # Save using standardized parameter loader
            ParameterLoader.save_outfit_parameters(self.current_ship, self.basic_grid)
            ParameterLoader.save_outfit_parameters(self.current_ship, self.physical_grid)
            ParameterLoader.save_outfit_parameters(self.current_ship, self.performance_grid)
            ParameterLoader.save_outfit_parameters(self.current_ship, self.defense_grid)
            ParameterLoader.save_outfit_parameters(self.current_ship, self.power_grid)
            ParameterLoader.save_outfit_parameters(self.current_ship, self.fuel_grid)
            ParameterLoader.save_outfit_parameters(self.current_ship, self.visual_grid)
            ParameterLoader.save_outfit_parameters(self.current_ship, self.manufacturer_grid)
            
            # Save default outfits from dropdowns
            self.current_ship.default_outfits = {}
            for outfit_type, var in self.outfit_vars.items():
                outfit_id = var.get().strip()
                if outfit_id:  # Only add non-empty selections
                    self.current_ship.default_outfits[outfit_id] = 1

            # Save animation endpoints
            animation_endpoints = self.animation_endpoints.save_endpoints()
            if animation_endpoints:
                self.current_ship.animation_endpoints = animation_endpoints
            elif hasattr(self.current_ship, 'animation_endpoints'):
                # Remove empty animation_endpoints to keep JSON clean
                delattr(self.current_ship, 'animation_endpoints')

            # Save description
            self.current_ship.description = self.description_text.get(1.0, tk.END).strip()

            # Set sprite metadata based on ship size if sprite is provided
            sprite = getattr(self.current_ship, 'sprite', '')
            if sprite:
                size_to_sprite_size = {
                    'small': 32,
                    'medium': 48,
                    'large': 64,
                    'capital': 256
                }
                ship_size = self.current_ship.size
                if not hasattr(self.current_ship, 'sprite_size') or self.current_ship.sprite_size == 0:
                    self.current_ship.sprite_size = size_to_sprite_size.get(ship_size, 32)
                if not hasattr(self.current_ship, 'animation_frames') or self.current_ship.animation_frames == 0:
                    self.current_ship.animation_frames = 32  # Standard rotation frames

            messagebox.showinfo("Success", f"Saved ship: {self.current_ship.name}")
            self.load_data()  # Refresh the list
            self.data_manager.auto_save()  # Auto-save changes

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save ship: {e}")

    def create_new_ship(self):
        """Create a new ship."""
        ship_id = simpledialog.askstring("New Ship", "Enter a unique ID for the new ship:")
        if not ship_id:
            return

        ships = self.data_manager.get_all_ships()
        if ship_id in ships:
            messagebox.showerror("Error", f"A ship with ID '{ship_id}' already exists")
            return

        try:
            # Create a comprehensive ship object with all game engine properties
            class ComprehensiveShip:
                def __init__(self, id, name):
                    # Basic properties
                    self.id = id
                    self.name = name
                    self.ship_class = "fighter"
                    self.size = "small"
                    self.cost = 75000
                    self.min_tech_level = 1
                    
                    # Physical properties
                    self.outfit_space = 40
                    self.cargo_space = 20
                    self.mass = 1.5
                    
                    # Performance properties
                    self.max_speed = 4.5
                    self.acceleration = 1.2
                    self.turn_rate = 2.0
                    
                    # Defense properties (current and max values)
                    self.shields = 120
                    self.max_shields = 120
                    self.armor = 80
                    self.max_armor = 80
                    self.shield_recharge_rate = 2.5
                    
                    # Power system properties
                    self.power_capacity = 100
                    self.power_regen_rate = 5.0
                    self.thruster_power_cost = 8.0
                    self.weapon_power_cost_base = 12.0
                    self.shield_regen_power_cost = 3.0
                    
                    # Fuel system properties
                    self.fuel_capacity = 150
                    self.fuel_consumption_rate = 1.0
                    self.jump_fuel_cost = 25.0
                    
                    # Visual properties
                    self.shipyard_image = ""
                    self.shipyard_display_image = ""
                    self.sprite = ""
                    self.sprite_size = 32  # Default for small ships
                    self.animation_frames = 32
                    
                    # Manufacturer properties
                    self.manufacturer = "Independent Shipyards"
                    self.origin = "Core Worlds"
                    self.availability = "common"
                    
                    # Equipment properties
                    self.default_outfits = {
                        "laser_cannon": 1
                    }
                    
                    # Description
                    self.description = "A versatile small ship suitable for various missions."

            new_ship = ComprehensiveShip(ship_id, ship_id.replace('_', ' ').title())

            # Add to ships registry
            self.data_manager.ships_registry[ship_id] = new_ship

            self.load_data()
            messagebox.showinfo("Success", f"Created new ship: {new_ship.name}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create ship: {e}")

    def delete_ship(self):
        """Delete the selected ship."""
        selection = self.listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "No ship selected to delete")
            return

        # Get selected ship
        ships = list(self.data_manager.get_all_ships().items())

        if selection[0] < len(ships):
            ship_id, ship = ships[selection[0]]

            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete '{ship.name}'?")
            if result:
                del self.data_manager.ships_registry[ship_id]
                self.load_data()
                messagebox.showinfo("Success", f"Deleted ship: {ship.name}")

    def export_ships(self):
        """Export ships to JSON file."""
        filename = filedialog.asksaveasfilename(
            title="Export Ships",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                ships_data = {}
                ships = self.data_manager.get_all_ships()

                for ship_id, ship in ships.items():
                    ships_data[ship_id] = self.data_manager._ship_to_dict(ship)

                with open(filename, 'w') as f:
                    json.dump(ships_data, f, indent=2)

                messagebox.showinfo("Success", f"Exported {len(ships_data)} ships to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export ships: {e}")
    
    def refresh_outfit_dropdowns(self):
        """Refresh the outfit dropdowns with current data from registry."""
        try:
            import sys, os
            game_src_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "src")
            if game_src_path not in sys.path:
                sys.path.insert(0, game_src_path)
            from game_objects.standardized_outfits import OUTFITS_REGISTRY
            
            # Regenerate outfit lists
            weapons = [""] + [f"{oid}" for oid, o in OUTFITS_REGISTRY.items() if o.category == "weapons"]
            defense = [""] + [f"{oid}" for oid, o in OUTFITS_REGISTRY.items() if o.category == "defense"]
            ammo = [""] + [f"{oid}" for oid, o in OUTFITS_REGISTRY.items() if o.category == "ammunition"]
            
            # Update dropdown values if dropdowns exist
            if hasattr(self, 'outfit_vars'):
                # Find the comboboxes and update their values
                # We need to find the actual combobox widgets and update them
                for child in self.frame.winfo_children():
                    self._update_comboboxes_recursive(child, weapons, defense, ammo)
            
            print(f"Ship Editor: Refreshed dropdowns - {len(weapons)-1} weapons, {len(defense)-1} defense, {len(ammo)-1} ammo")
                
        except Exception as e:
            print(f"Ship Editor: Error refreshing dropdowns: {e}")
    
    def _update_comboboxes_recursive(self, widget, weapons, defense, ammo):
        """Recursively find and update comboboxes."""
        try:
            # Check if this widget is a combobox
            if isinstance(widget, ttk.Combobox):
                # Get the variable associated with this combobox
                var = widget.cget('textvariable')
                if var:
                    # Check which type of dropdown this is based on the variable
                    for var_name, var_obj in self.outfit_vars.items():
                        if str(var_obj) == str(var):
                            if var_name in ['weapon1', 'weapon2']:
                                widget.configure(values=weapons)
                                print(f"  Updated {var_name} dropdown with {len(weapons)} options")
                            elif var_name == 'defense':
                                widget.configure(values=defense)
                                print(f"  Updated {var_name} dropdown with {len(defense)} options")
                            elif var_name == 'ammo':
                                widget.configure(values=ammo)
                                print(f"  Updated {var_name} dropdown with {len(ammo)} options")
                            break
            
            # Recursively check children
            for child in widget.winfo_children():
                self._update_comboboxes_recursive(child, weapons, defense, ammo)
                
        except Exception as e:
            # Silently ignore errors in recursive traversal
            pass
