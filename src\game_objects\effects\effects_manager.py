"""
Effects Manager for Escape Velocity Py
Handles all visual and audio effects in the game.
"""

import pygame as pg
import os
from pathlib import Path
from game_objects.sprite_manager import SpriteSheet

class Effect(pg.sprite.Sprite):
    """Base class for all visual effects."""
    
    def __init__(self, game, pos, effect_type="explosion", scale=1.0, duration=1.0, sound=None):
        """
        Initialize an effect.
        
        Args:
            game: The game instance
            pos: Position as pg.math.Vector2
            effect_type: Type of effect ("explosion", "laser_hit", "shield_hit", etc.)
            scale: Scale multiplier for the effect
            duration: Duration in seconds
            sound: Sound file to play (optional)
        """
        super().__init__()
        self.game = game
        self.pos = pg.math.Vector2(pos)
        self.effect_type = effect_type
        self.scale = scale
        self.duration = duration
        self.time_alive = 0.0
        self.finished = False
        
        # Animation properties
        self.current_frame = 0
        self.frame_rate = 15.0  # Frames per second for animation
        self.frame_timer = 0.0
        
        # Load effect spritesheet
        self.spritesheet = None
        self.frames = []
        self.load_effect_sprites()
        
        # Set up initial image and rect
        if self.frames:
            self.image = self.frames[0]
            if scale != 1.0:
                w, h = self.image.get_size()
                self.image = pg.transform.scale(self.image, (int(w * scale), int(h * scale)))
        else:
            # Fallback to colored circle if no sprites found
            self.create_fallback_image()
            
        self.rect = self.image.get_rect(center=self.pos)
        
        # Play sound effect if provided
        if sound and hasattr(game, 'sound_manager'):
            game.sound_manager.play_sound(sound, "weapons")
    
    def load_effect_sprites(self):
        """Load the spritesheet for this effect type."""
        # Try to load from effects directory
        base_path = Path("assets/images/sprites/effects")
        sprite_path = base_path / f"{self.effect_type}_spritesheet.png"
        
        if sprite_path.exists():
            # Use the SpriteSheet class which handles your standard format
            self.spritesheet = SpriteSheet(str(sprite_path))
            if self.spritesheet and self.spritesheet.frames:
                # Load all frames from spritesheet
                for i in range(len(self.spritesheet.frames)):
                    frame = self.spritesheet.get_frame(i)
                    if frame:
                        self.frames.append(frame)
        
        # If no sprites found, we'll use fallback
        if not self.frames:
            print(f"No sprites found for effect: {self.effect_type}, using fallback")
    
    def create_fallback_image(self):
        """Create a simple fallback effect when no sprites are available."""
        size = int(32 * self.scale)
        self.image = pg.Surface((size, size), pg.SRCALPHA)
        
        # Different colors for different effect types
        if self.effect_type == "explosion":
            color = (255, 100, 0, 200)  # Orange
        elif self.effect_type == "laser_hit":
            color = (255, 255, 100, 200)  # Yellow
        elif self.effect_type == "shield_hit":
            color = (100, 100, 255, 200)  # Blue
        else:
            color = (255, 255, 255, 200)  # White
        
        pg.draw.circle(self.image, color, (size//2, size//2), size//2)
    
    def update(self, dt):
        """Update the effect animation."""
        self.time_alive += dt
        
        # Check if effect has finished
        if self.time_alive >= self.duration:
            self.finished = True
            return True
        
        # Update animation frame
        if self.frames:
            self.frame_timer += dt
            if self.frame_timer >= 1.0 / self.frame_rate:
                self.frame_timer = 0.0
                self.current_frame += 1
                
                if self.current_frame >= len(self.frames):
                    # Loop or finish based on effect type
                    if self.effect_type in ["explosion", "laser_hit", "missile_hit"]:
                        self.finished = True
                        return True
                    else:
                        self.current_frame = 0  # Loop for continuous effects
                
                # Update image
                self.image = self.frames[self.current_frame]
                if self.scale != 1.0:
                    w, h = self.image.get_size()
                    self.image = pg.transform.scale(self.image, (int(w * self.scale), int(h * self.scale)))
                
                # Update rect to maintain center position
                old_center = self.rect.center
                self.rect = self.image.get_rect()
                self.rect.center = old_center
        
        return False
    
    def draw(self, screen, camera_offset=(0, 0)):
        """Draw the effect to the screen with camera offset."""
        if not self.finished and self.image:
            # Calculate screen position accounting for camera offset
            screen_pos = (self.pos.x - camera_offset[0], self.pos.y - camera_offset[1])
            
            # Update rect center to screen position
            draw_rect = self.rect.copy()
            draw_rect.center = screen_pos
            
            # Draw the effect
            screen.blit(self.image, draw_rect)


class EffectsManager:
    """Manages all visual and audio effects in the game."""
    
    def __init__(self, game):
        """Initialize the effects manager."""
        self.game = game
        self.active_effects = pg.sprite.Group()
        
        # Effect definitions with default properties
        self.effect_definitions = {
            "explosion": {
                "duration": 0.8,
                "scale": 1.0,
                "sound": "explosion",
                "frame_rate": 20.0
            },
            "small_explosion": {
                "duration": 0.5,
                "scale": 0.6,
                "sound": "small_explosion",
                "frame_rate": 18.0
            },
            "large_explosion": {
                "duration": 1.2,
                "scale": 1.8,
                "sound": "large_explosion",
                "frame_rate": 15.0
            },
            "laser_hit": {
                "duration": 0.3,
                "scale": 0.8,
                "sound": "laser_hit",
                "frame_rate": 25.0
            },
            "missile_hit": {
                "duration": 0.6,
                "scale": 1.2,
                "sound": "missile_hit",
                "frame_rate": 20.0
            },
            "shield_hit": {
                "duration": 0.4,
                "scale": 1.0,
                "sound": "shield_hit",
                "frame_rate": 20.0
            },
            "engine_trail": {
                "duration": 0.8,
                "scale": 0.7,
                "sound": None,
                "frame_rate": 30.0
            },
            "muzzle_flash": {
                "duration": 0.15,
                "scale": 0.5,
                "sound": None,
                "frame_rate": 30.0
            },
            "warp_effect": {
                "duration": 2.0,
                "scale": 2.0,
                "sound": "warp",
                "frame_rate": 12.0
            }
        }
        
        print("EffectsManager initialized")
    
    def create_effect(self, effect_type, pos, scale=None, duration=None, sound=None):
        """
        Create and add a new effect.
        
        Args:
            effect_type: Type of effect to create
            pos: Position as tuple or Vector2
            scale: Optional scale override
            duration: Optional duration override  
            sound: Optional sound override
        
        Returns:
            The created Effect object
        """
        # Get default properties for this effect type
        defaults = self.effect_definitions.get(effect_type, {
            "duration": 1.0,
            "scale": 1.0, 
            "sound": None,
            "frame_rate": 15.0
        })
        
        # Use provided values or defaults
        final_scale = scale if scale is not None else defaults["scale"]
        final_duration = duration if duration is not None else defaults["duration"]
        final_sound = sound if sound is not None else defaults["sound"]
        
        # Create the effect
        effect = Effect(
            self.game,
            pos,
            effect_type,
            final_scale,
            final_duration,
            final_sound
        )
        
        # Set frame rate if available
        if "frame_rate" in defaults:
            effect.frame_rate = defaults["frame_rate"]
        
        # Add to active effects
        self.active_effects.add(effect)
        
        return effect
    
    def explosion(self, pos, size="normal"):
        """Create an explosion effect."""
        if size == "small":
            return self.create_effect("small_explosion", pos)
        elif size == "large":
            return self.create_effect("large_explosion", pos)
        else:
            return self.create_effect("explosion", pos)
    
    def laser_hit(self, pos):
        """Create a laser hit effect."""
        return self.create_effect("laser_hit", pos)
    
    def missile_hit(self, pos):
        """Create a missile hit effect."""
        return self.create_effect("missile_hit", pos)

    def shield_hit(self, pos):
        """Create a shield hit effect."""
        return self.create_effect("shield_hit", pos)

    def engine_trail(self, pos):
        """Create an engine trail effect."""
        return self.create_effect("engine_trail", pos)

    def muzzle_flash(self, pos):
        """Create a muzzle flash effect."""
        return self.create_effect("muzzle_flash", pos)

    def warp_effect(self, pos):
        """Create a warp effect."""
        return self.create_effect("warp_effect", pos)

    def update(self, dt):
        """Update all active effects."""
        # Update effects and remove finished ones
        finished_effects = []
        for effect in self.active_effects:
            if effect.update(dt):
                finished_effects.append(effect)

        # Remove finished effects
        for effect in finished_effects:
            self.active_effects.remove(effect)

    def draw(self, screen, camera_offset=(0, 0)):
        """Draw all active effects."""
        for effect in self.active_effects:
            effect.draw(screen, camera_offset)

    def get_available_effects(self):
        """Get list of available effect types."""
        return list(self.effect_definitions.keys())

    def load_effects_from_directory(self, effects_dir):
        """
        Load available effects from sprites directory.
        This will scan for effect spritesheets and add them to available effects.
        """
        import os
        if not os.path.exists(effects_dir):
            print(f"Effects directory not found: {effects_dir}")
            return

        # Scan for effect spritesheets
        for filename in os.listdir(effects_dir):
            if filename.endswith('_spritesheet.png'):
                effect_name = filename.replace('_spritesheet.png', '')

                # Add to effect definitions if not already present
                if effect_name not in self.effect_definitions:
                    self.effect_definitions[effect_name] = {
                        "duration": 1.0,
                        "scale": 1.0,
                        "sound": None,
                        "frame_rate": 15.0
                    }
                    print(f"Auto-discovered effect: {effect_name}")

    def create_positioned_effect(self, effect_name, base_position, position_type, offset, entity):
        """
        Create an effect with calculated positioning.
        This method integrates with the PositionCalculator.
        """
        from ..position_calculator import PositionCalculator

        calculated_position = PositionCalculator.calculate_position(
            entity, position_type, offset
        )

        return self.create_effect(effect_name, calculated_position)

    def start_continuous_effect(self, entity, effect_name, position_config):
        """
        Start a continuous effect that follows an entity.
        Returns the effect instance for tracking.
        """
        effect = self.create_effect(effect_name, entity.pos)

        # Mark as continuous (we'll need to track this differently)
        if hasattr(effect, 'continuous'):
            effect.continuous = True
            effect.entity = entity
            effect.position_config = position_config

        return effect

    def stop_continuous_effect(self, entity, effect_name):
        """Stop a continuous effect for an entity."""
        for effect in list(self.active_effects):
            if (hasattr(effect, 'continuous') and effect.continuous and
                hasattr(effect, 'entity') and effect.entity == entity and
                effect.effect_type == effect_name):
                effect.finished = True