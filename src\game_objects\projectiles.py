"""
Projectile definitions for Escape Velocity Py.
This module contains all projectile types that can be fired by weapons.
"""

import pygame as pg
import math
from game_objects.outfits import WEAPON_TYPE_LASER, WEAPON_TYPE_MISSILE, WEAPON_TYPE_PROJECTILE, WEAPON_TYPE_BEAM
from game_objects.sprite_manager import load_sprite

# Projectile types
PROJECTILE_TYPE_LASER = "laser"
PROJECTILE_TYPE_MISSILE = "missile"
PROJECTILE_TYPE_PROJECTILE = "projectile"
PROJECTILE_TYPE_BEAM = "beam"

# Projectile colors
LASER_COLOR = (255, 0, 0)  # Red
MISSILE_COLOR = (255, 128, 0)  # Orange
PROJECTILE_COLOR = (0, 255, 255)  # Cyan
BEAM_COLOR = (255, 0, 255)  # Magenta

class Projectile(pg.sprite.Sprite):
    """Base class for all projectiles."""

    def __init__(self, game, owner, pos, angle, speed, shield_damage, armor_damage, range, color=(255, 255, 255),
                 size=(4, 4), lifetime=None, image_path=None):
        """
        Initialize a projectile.

        Args:
            game: The game instance
            owner: The entity that fired the projectile
            pos (Vector2): Starting position
            angle (float): Direction angle in degrees
            speed (float): Projectile speed
            damage (float): Damage on hit
            range (float): Maximum travel distance
            color (tuple): RGB color for default rendering
            size (tuple): Width and height for default rendering
            lifetime (float): Maximum lifetime in seconds (if None, calculated from range/speed)
            image_path (str): Path to projectile image (optional)
        """
        super().__init__()
        self.game = game
        self.owner = owner
        self.pos = pg.math.Vector2(pos)
        self.angle = angle
        self.speed = speed
        self.shield_damage = shield_damage
        self.armor_damage = armor_damage
        self.range = range
        self.color = color
        self.size = size

        # Calculate direction vector from angle (0 degrees is North/Up)
        # Convert to math angle (0 degrees is East/Right) for cos/sin, then adjust for Pygame's Y-down
        math_angle_rad = math.radians(self.angle - 90) # e.g. North (0) becomes -90 deg, East (90) becomes 0 deg
        self.direction = pg.math.Vector2(
            math.cos(math_angle_rad),
            math.sin(math_angle_rad) # For Pygame: positive sin() is downwards, negative sin() is upwards.
                                     # A ship angle of 0 (North) means math_angle -90, sin(-90) = -1 (upwards)
                                     # A ship angle of 90 (East) means math_angle 0, cos(0) = 1 (rightwards)
        ).normalize()

        # Calculate lifetime based on range and speed if not provided
        self.lifetime = lifetime if lifetime is not None else range / speed
        self.time_alive = 0
        self.distance_traveled = 0
        self.start_pos = pg.math.Vector2(pos)

        # Load image or create default
        if image_path:
            try:
                self.image = pg.image.load(image_path).convert_alpha()
            except:
                self.create_default_image()
        else:
            self.create_default_image()

        self.rect = self.image.get_rect()
        self.rect.center = self.pos

    def create_default_image(self):
        """Create a default image for the projectile."""
        self.image = pg.Surface(self.size, pg.SRCALPHA)
        pg.draw.rect(self.image, self.color, (0, 0, self.size[0], self.size[1]))

    def update(self, dt):
        """
        Update projectile position and check for collisions.

        Args:
            dt (float): Time delta in seconds

        Returns:
            bool: True if the projectile should be removed
        """
        # Update lifetime and check if expired
        self.time_alive += dt
        if self.time_alive >= self.lifetime:
            self._trigger_timeout_effects()
            return True  # Remove projectile

        # Move projectile
        movement = self.direction * self.speed * dt
        self.pos += movement
        self.distance_traveled += movement.length()

        # Check if out of range
        if self.distance_traveled >= self.range:
            self._trigger_timeout_effects()
            return True  # Remove projectile

        # Update rect position
        self.rect.center = self.pos

        # Check for collisions - FIXED: Remove immediately on hit
        if self.check_collisions():
            return True  # Remove projectile immediately

        return False

    def check_collisions(self):
        """
        Check for collisions with other entities.

        Returns:
            bool: True if collision occurred
        """
        # Base implementation - override in subclasses
        return False

    def on_hit(self, target):
        """
        Handle collision with a target.

        Args:
            target: The entity that was hit
        """
        # Trigger impact animation effects
        self._trigger_impact_effects(target)

        # Apply damage if target has a take_damage method
        if hasattr(target, 'take_damage'):
            # Determine attacker faction for proper combat response
            if hasattr(self.owner, 'faction_id'):
                attacker_faction = self.owner.faction_id
            elif self.owner == self.game.player:
                attacker_faction = "player"
            else:
                attacker_faction = "unknown"

            print(f"DEBUG: Projectile hit! Owner: {type(self.owner).__name__}, Attacker faction: {attacker_faction}, Target: {type(target).__name__}")

            # Check method signature to handle different ship types
            import inspect
            sig = inspect.signature(target.take_damage)
            if len(sig.parameters) >= 3:  # Player ship (shield_damage, armor_damage, attacker_faction)
                target.take_damage(self.shield_damage, self.armor_damage, attacker_faction)
            else:  # AI ship (total_damage, attacker_damage=None)
                total_damage = self.shield_damage + self.armor_damage
                target.take_damage(total_damage, attacker_faction)

    def _trigger_impact_effects(self, target):
        """Trigger animation effects for projectile impact."""
        if hasattr(self.game, 'animation_integration') and self.game.animation_integration:
            # Set projectile properties for animation lookup
            if hasattr(self, 'weapon_source'):
                self.weapon_source.outfit_id = self.weapon_source.id
                self.weapon_source.category = 'weapons'
            
            animation_integration = self.game.animation_integration
            animation_integration.trigger_projectile_impact(self, tuple(self.pos))

    def _trigger_timeout_effects(self):
        """Trigger animation effects for projectile timeout."""
        if hasattr(self.game, 'animation_integration') and self.game.animation_integration:
            animation_integration = self.game.animation_integration
            animation_integration.trigger_projectile_timeout(self)

    def start_trail_effects(self):
        """Start trail effects for this projectile."""
        if hasattr(self.game, 'animation_integration') and self.game.animation_integration:
            # Set properties for animation lookup
            if hasattr(self, 'weapon_source'):
                # Use ammo properties for trail effects
                ammo_id = getattr(self.weapon_source, 'loaded_ammo_type', None)
                if ammo_id:
                    self.outfit_id = ammo_id
                    self.category = 'ammunition'
                    
                    animation_integration = self.game.animation_integration
                    animation_integration.trigger_projectile_trail(self)




class LaserProjectile(Projectile):
    """Laser projectile - fast and straight."""

    def __init__(self, game, owner, pos, angle, shield_damage, armor_damage, range, color=LASER_COLOR, beam=False, image_path=None):
        """
        Initialize a laser projectile.

        Args:
            game: The game instance
            owner: The entity that fired the laser
            pos (Vector2): Starting position
            angle (float): Direction angle in degrees
            damage (float): Damage on hit
            range (float): Maximum travel distance
            color (tuple): RGB color for the laser
            beam (bool): Whether this is a continuous beam weapon
        """
        # Lasers are fast
        speed = 800

        # Beam weapons are longer
        if beam:
            size = (range, 2)  # Very long beam
            color = (color[0], color[1], color[2], 180)  # Semi-transparent
        else:
            size = (8, 2)  # Standard laser bolt

        # Rotate size based on angle
        if 45 < angle % 180 < 135:
            size = (size[1], size[0])  # Swap dimensions for vertical orientation

        super().__init__(game, owner, pos, angle, speed, shield_damage, armor_damage, range, color, size, image_path=image_path)

        # Load spritesheet and get frame matching initial angle
        self.spritesheet = None
        self.image_orig = None
        if image_path:
            try:
                sprite_name = image_path.split('/')[-1].replace('_spritesheet.png', '')
                self.spritesheet = load_sprite("projectile", sprite_name, "")
                if self.spritesheet and self.spritesheet.image:
                    # Get the frame that matches the initial angle
                    initial_frame = self.spritesheet.get_frame_by_angle(self.angle)
                    if initial_frame:
                        self.image_orig = initial_frame.copy()
                        self.image = initial_frame.copy()
                        self.rect = self.image.get_rect(center=self.pos)
                    else:
                        # Fallback to first frame
                        first_frame = self.spritesheet.get_frame(0)
                        if first_frame:
                            self.image_orig = first_frame.copy()
                            self.image = first_frame.copy()
                            self.rect = self.image.get_rect(center=self.pos)
            except Exception as e:
                print(f"Error loading laser spritesheet: {e}")
        
        if not self.image_orig:
            self.image_orig = self.image.copy()

        self.beam = beam

        # For beam weapons, reduce lifetime
        if beam:
            self.lifetime = 0.1  # Short lifetime for beam weapons

        # Rotate the image to match the angle
        self.image = pg.transform.rotate(self.image, angle)
        self.rect = self.image.get_rect(center=self.pos)

    def create_default_image(self):
        """Create a laser beam image."""
        self.image = pg.Surface(self.size, pg.SRCALPHA)

        if hasattr(self, 'beam') and self.beam:
            # For beam weapons, create a gradient effect
            for i in range(self.size[0]):
                # Fade out towards the end
                alpha = max(0, 255 - (i / self.size[0]) * 200)
                color = (self.color[0], self.color[1], self.color[2], int(alpha))
                pg.draw.line(self.image, color, (i, 0), (i, self.size[1]), 1)
        else:
            # Standard laser bolt
            pg.draw.rect(self.image, self.color, (0, 0, self.size[0], self.size[1]))

    def check_collisions(self):
        """Check for collisions with ships and planets."""
        # Don't collide with the owner
        for ship in self.game.ai_ships:
            if ship != self.owner and ship.rect.colliderect(self.rect):
                self.on_hit(ship)
                return True  # Remove projectile on hit

        # Check collision with player if not fired by player
        if self.owner != self.game.player and self.game.player.rect.colliderect(self.rect):
            self.on_hit(self.game.player)
            return True  # Remove projectile on hit

        # Check collision with planets
        for planet in self.game.planets:
            if planet.rect.colliderect(self.rect):
                # Optional: could add visual effect for hitting a planet
                return True  # Remove projectile on hit

        return False


class MissileProjectile(Projectile):
    """Guided missile that homes in on a target."""

    def __init__(self, game, owner, pos, angle, shield_damage, armor_damage, range, target=None,
                 tracking=0.8, color=MISSILE_COLOR, delay_time=0.0, projectile_speed=300, image_path=None):
        """
        Initialize a missile projectile.

        Args:
            game: The game instance
            owner: The entity that fired the missile
            pos (Vector2): Starting position
            angle (float): Initial direction angle in degrees
            shield_damage (float): Shield damage on hit
            armor_damage (float): Armor damage on hit
            range (float): Maximum travel distance
            target: The target to track (optional)
            tracking (float): How well the missile tracks its target (0.0-1.0)
            color (tuple): RGB color for default rendering
            projectile_speed (int): Speed from ammo specifications
            image_path (str): Path to projectile sprite
        """
        # Use ammo-specified speed instead of hardcoded value
        speed = projectile_speed
        size = (6, 6)

        super().__init__(game, owner, pos, angle, speed, shield_damage, armor_damage, range, color, size, image_path=image_path)

        # Delayed launch system
        self.delay_time = delay_time
        self.launch_phase = 'coasting' if delay_time > 0 else 'active'
        self.time_since_launch = 0.0

        self.target = target
        self.tracking = tracking  # How well the missile tracks (0.0-1.0)
        # CRITICAL FIX: Use ammo specs instead of hardcoded values
        self.turn_rate = 120 * tracking if tracking > 0 else 0  # Only turn if tracking > 0
        self.acceleration = 100 * tracking if tracking > 0 else 0  # Only accelerate if tracking > 0
        self.max_speed = min(speed * 2, 800)  # Max speed based on initial speed, capped at 800
        self.current_speed = speed * 0.8  # Start at 80% of ammo speed
        self.guided = tracking > 0.0  # Only guided if tracking > 0

        # Load spritesheet and get initial frame
        self.spritesheet = None
        self.original_image = None
        if image_path:
            try:
                # CRITICAL FIX: Handle both absolute and relative paths
                if image_path.startswith('C:') or image_path.startswith('/'):
                    # Absolute path from JSON - extract sprite name
                    sprite_name = image_path.split('/')[-1].replace('_spritesheet.png', '')
                else:
                    # Relative path - use as is
                    sprite_name = image_path.replace('_spritesheet.png', '')
                    
                self.spritesheet = load_sprite("projectile", sprite_name, "")
                if self.spritesheet and self.spritesheet.image:
                    # Get the frame that matches the initial angle
                    initial_frame = self.spritesheet.get_frame_by_angle(self.angle)
                    if initial_frame:
                        self.image = initial_frame
                        self.original_image = initial_frame.copy()
                        self.rect = self.image.get_rect(center=self.pos)
                    else:
                        # Fallback to first frame
                        first_frame = self.spritesheet.get_frame(0)
                        if first_frame:
                            self.image = first_frame
                            self.original_image = first_frame.copy()
                            self.rect = self.image.get_rect(center=self.pos)
            except Exception as e:
                print(f"Error loading missile spritesheet '{image_path}': {e}")
        
        # If spritesheet failed, use the current image as original
        if not self.original_image:
            self.original_image = self.image.copy()

    def rotate_image(self):
        """Rotate projectile image to match current angle."""
        if self.spritesheet and self.spritesheet.image:
            # Use spritesheet rotation like ships - get frame matching current angle
            frame = self.spritesheet.get_frame_by_angle(self.angle)
            if frame:
                self.image = frame
                # Update rect to maintain center position
                old_center = self.rect.center
                self.rect = self.image.get_rect()
                self.rect.center = old_center
                return
        
        # Fallback to transform rotation
        if self.original_image:
            self.image = pg.transform.rotate(self.original_image, -self.angle)
            # Update rect to maintain center position  
            old_center = self.rect.center
            self.rect = self.image.get_rect()
            self.rect.center = old_center

    def create_default_image(self):
        """Create a missile image."""
        self.image = pg.Surface((8, 8), pg.SRCALPHA)
        # Draw a small triangle for the missile
        pg.draw.polygon(self.image, self.color, [(4, 0), (0, 8), (8, 8)])

    def update(self, dt):
        """Update missile position and tracking."""
        # Handle delayed launch phases
        self.time_since_launch += dt
        
        # During coasting phase, just drift at very low speed
        if self.launch_phase == 'coasting':
            if self.time_since_launch >= self.delay_time:
                self.launch_phase = 'active'
                # Reset to full speed when activating
                self.current_speed = self.speed
            else:
                # Just drift slowly in original direction during delay
                self.current_speed = 50
                # Skip all guidance/tracking logic during coasting
                movement = self.direction * self.current_speed * dt
                self.pos += movement
                self.distance_traveled += movement.length()
                
                # Update lifetime and check if expired
                self.time_alive += dt
                if self.time_alive >= self.lifetime or self.distance_traveled >= self.range:
                    self._trigger_timeout_effects()
                    return True
                    
                self.rect.center = self.pos
                return False  # Don't do collision checking during coasting
        
        # ACTIVE PHASE - Normal missile behavior
        if self.target and hasattr(self.target, 'alive') and self.target.alive() and self.tracking > 0:
            # Calculate direction to target
            to_target = self.target.pos - self.pos
            if to_target.length_squared() > 0:
                to_target = to_target.normalize()

                # Calculate angle to target
                target_angle = math.degrees(math.atan2(-to_target.y, to_target.x))

                # Calculate angle difference (shortest path)
                angle_diff = (target_angle - self.angle) % 360
                if angle_diff > 180:
                    angle_diff -= 360

                # Turn towards target with limited turn rate
                turn_amount = min(abs(angle_diff), self.turn_rate * dt)
                if angle_diff < 0:
                    self.angle -= turn_amount
                else:
                    self.angle += turn_amount

                # Recalculate direction vector
                self.direction = pg.math.Vector2(
                    math.cos(math.radians(self.angle)),
                    -math.sin(math.radians(self.angle))
                ).normalize()

                # Accelerate
                self.current_speed = min(self.current_speed + self.acceleration * dt, self.max_speed)

                # Rotate image to match direction
                self.rotate_image()
                self.rect = self.image.get_rect(center=self.pos)

        # Use current speed instead of constant speed
        movement = self.direction * self.current_speed * dt
        self.pos += movement
        self.distance_traveled += movement.length()

        # Update lifetime and check if expired
        self.time_alive += dt
        if self.time_alive >= self.lifetime or self.distance_traveled >= self.range:
            self._trigger_timeout_effects()
            return True  # Remove projectile

        # Update rect position
        self.rect.center = self.pos

        # Check for collisions
        if self.check_collisions():
            return True  # Remove projectile

        return False

    def check_collisions(self):
        """Check for collisions with ships and planets."""
        # Don't collide with the owner
        for ship in self.game.ai_ships:
            if ship != self.owner and ship.rect.colliderect(self.rect):
                self.on_hit(ship)
                return True  # Remove projectile on hit

        # Check collision with player if not fired by player
        if self.owner != self.game.player and self.game.player.rect.colliderect(self.rect):
            self.on_hit(self.game.player)
            return True  # Remove projectile on hit

        # Check collision with planets
        for planet in self.game.planets:
            if planet.rect.colliderect(self.rect):
                # Optional: could add explosion effect
                return True  # Remove projectile on hit

        return False
