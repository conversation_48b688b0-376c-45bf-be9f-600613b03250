"""
Base Editor for Outfit Categories
Provides common functionality for all outfit editors
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json

class BaseOutfitEditor:
    """Base class for all outfit editors."""
    
    def __init__(self, parent, data_manager, category_name):
        self.parent = parent
        self.data_manager = data_manager
        self.category_name = category_name
        self.current_outfit = None
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Setup UI
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the basic UI structure."""
        # Create horizontal layout
        paned = ttk.PanedWindow(self.frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Item list
        left_frame = ttk.LabelFrame(paned, text=f"{self.category_name.title()} Library")
        paned.add(left_frame, weight=1)
        
        # Item listbox
        self.listbox = tk.Listbox(left_frame)
        self.listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.listbox.bind("<<ListboxSelect>>", self.on_item_select)
        
        # Buttons for item management
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text=f"New {self.category_name.title()}", 
                  command=self.create_new_item).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Delete", 
                  command=self.delete_item).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Export", 
                  command=self.export_items).pack(side=tk.LEFT, padx=2)
        
        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text=f"{self.category_name.title()} Editor")
        paned.add(right_frame, weight=2)
        
        # Setup specific editor UI (to be implemented by subclasses)
        self.setup_editor_ui(right_frame)
    
    def setup_editor_ui(self, parent):
        """Setup the specific editor UI. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement setup_editor_ui")
    
    def load_data(self):
        """Load items into the listbox."""
        if hasattr(self, 'listbox'):
            # Store current selection if any
            selected_index = getattr(self, 'selected_index', None)
            
            self.listbox.delete(0, tk.END)
            
            items = self.data_manager.get_outfits_by_category(self.category_name)
            for outfit_id, outfit in items.items():
                display_name = outfit.name
                self.listbox.insert(tk.END, display_name)
            
            # CRITICAL FIX: Restore selection after refresh
            if selected_index is not None and selected_index < self.listbox.size():
                self.listbox.selection_set(selected_index)
                self.listbox.activate(selected_index)
                self.listbox.see(selected_index)  # Ensure visible
    
    def on_item_select(self, event=None):
        """Handle item selection from list."""
        selection = self.listbox.curselection()
        if not selection:
            return
        
        # Store the selected index for visual feedback
        self.selected_index = selection[0]
        
        # Get selected item
        items = list(self.data_manager.get_outfits_by_category(self.category_name).values())
        
        if selection[0] < len(items):
            item = items[selection[0]]
            self.load_item_into_editor(item)
            
        # CRITICAL FIX: Maintain visual selection even when focus changes
        self.listbox.selection_set(selection[0])
        self.listbox.activate(selection[0])
    
    def load_item_into_editor(self, item):
        """Load item data into the editor. To be implemented by subclasses."""
        self.current_outfit = item
        # Subclasses should implement specific loading logic
    
    def save_item(self):
        """Save the current item. To be implemented by subclasses."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", f"No {self.category_name} selected to save")
            return

        # Store current outfit name and index for reselection
        current_outfit_name = self.current_outfit.name
        current_selection = self.listbox.curselection()
        current_index = current_selection[0] if current_selection else None

        # Subclasses should implement specific saving logic
        messagebox.showinfo("Success", f"Saved {self.category_name}: {self.current_outfit.name}")

        # Auto-save changes
        self.data_manager.auto_save()

        # Refresh the list to show any changes but preserve selection
        self.selected_index = current_index  # Store for restoration
        self.load_data()

        # Notify other editors to refresh their dropdowns (with delay to prevent loops)
        self.parent.after(100, self._notify_data_changed)
        
    def create_new_item(self):
        """Create a new item."""
        item_id = simpledialog.askstring(f"New {self.category_name.title()}", 
                                        f"Enter a unique ID for the new {self.category_name}:")
        if not item_id:
            return
        
        if item_id in self.data_manager.outfits_registry:
            messagebox.showerror("Error", f"A {self.category_name} with ID '{item_id}' already exists")
            return
        
        try:
            # Create new item (to be implemented by subclasses)
            new_item = self.create_new_item_instance(item_id)
            
            self.data_manager.outfits_registry[item_id] = new_item
            self.load_data()
            # CRITICAL FIX: Save new item to JSON file
            self.data_manager.auto_save()
            # Notify other editors to refresh their dropdowns (with delay to prevent loops)  
            self.parent.after(100, self._notify_data_changed)
            messagebox.showinfo("Success", f"Created new {self.category_name}: {new_item.name}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create {self.category_name}: {e}")
    
    def create_new_item_instance(self, item_id):
        """Create a new item instance. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement create_new_item_instance")
    
    def delete_item(self):
        """Delete the selected item."""
        selection = self.listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", f"No {self.category_name} selected to delete")
            return
        
        # Get selected item
        items = list(self.data_manager.get_outfits_by_category(self.category_name).items())
        
        if selection[0] < len(items):
            item_id, item = items[selection[0]]
            
            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete '{item.name}'?")
            if result:
                del self.data_manager.outfits_registry[item_id]
                self.load_data()
                # CRITICAL FIX: Save deletion to JSON file
                self.data_manager.auto_save()
                # Notify other editors to refresh their dropdowns (with delay to prevent loops)
                self.parent.after(100, self._notify_data_changed)
                messagebox.showinfo("Success", f"Deleted {self.category_name}: {item.name}")
    
    def export_items(self):
        """Export items to JSON file."""
        filename = filedialog.asksaveasfilename(
            title=f"Export {self.category_name.title()}",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                items_data = {}
                items = self.data_manager.get_outfits_by_category(self.category_name)
                
                for outfit_id, outfit in items.items():
                    items_data[outfit_id] = self.data_manager._outfit_to_dict(outfit)
                
                with open(filename, 'w') as f:
                    json.dump(items_data, f, indent=2)
                
                messagebox.showinfo("Success", f"Exported {len(items_data)} {self.category_name} to {filename}")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export {self.category_name}: {e}")
    
    def browse_image(self, var):
        """Browse for an image file and set the variable."""
        filename = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            try:
                import os
                relative_path = os.path.relpath(filename, os.getcwd())
                var.set(relative_path)
            except ValueError:
                var.set(filename)
    
    def _notify_data_changed(self):
        """Notify other editors that data has changed."""
        # Try to find the main window and refresh all dependent UI elements
        try:
            # Look for the main window in the parent hierarchy
            current = self.parent
            main_window = None
            while current and not hasattr(current, 'refresh_all_dropdowns'):
                current = getattr(current, 'master', None) or getattr(current, 'parent', None)
                if hasattr(current, 'refresh_all_dropdowns'):
                    main_window = current
                    break
            
            if main_window:
                main_window.refresh_all_dropdowns()
                print(f"Editor: Refreshed all dropdowns after {self.category_name} change")
            else:
                print(f"Editor: Could not find main window to refresh dropdowns")
                
        except Exception as e:
            print(f"Editor: Error refreshing dropdowns: {e}")
