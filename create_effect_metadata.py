"""
Simple Effect Creator (No pygame dependency)
Creates basic effect metadata files for testing animation endpoints.
"""

import json
import os


def create_effect_metadata(effect_name, frame_width, frame_height, frame_count):
    """Create metadata for an effect."""
    return {
        "ship_name": effect_name,
        "frame_count": frame_count,
        "sprite_size": frame_width,
        "sheet_width": frame_width * frame_count,
        "sheet_height": frame_height,
        "layout": "horizontal",
        "frames": [
            {
                "frame": i,
                "angle": 0.0,
                "x": i * frame_width,
                "y": 0,
                "width": frame_width,
                "height": frame_height
            }
            for i in range(frame_count)
        ]
    }


def main():
    """Create effect metadata files for testing."""
    # Create effects directory if it doesn't exist
    effects_dir = "assets/images/sprites/effects"
    os.makedirs(effects_dir, exist_ok=True)
    
    # Define basic effects that match your existing spritesheets
    effects = [
        ("engine_trail", 256, 256, 24),
        ("small_explosion", 32, 32, 32),
        ("explosion", 64, 64, 8),
        ("laser_hit", 32, 32, 6),
        ("missile_hit", 56, 56, 8),
        ("shield_hit", 48, 48, 5),
        ("muzzle_flash", 24, 24, 3),
        ("missile_launch", 32, 32, 4),
    ]
    
    print("Creating effect metadata files...")
    
    for effect_name, width, height, frames in effects:
        metadata = create_effect_metadata(effect_name, width, height, frames)
        metadata_path = os.path.join(effects_dir, f"{effect_name}_spritesheet.json")
        
        # Only create if metadata doesn't exist (don't overwrite existing files)
        if not os.path.exists(metadata_path):
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            print(f"Created {metadata_path}")
        else:
            print(f"Skipped {metadata_path} (already exists)")
    
    print("Effect metadata creation complete!")


if __name__ == "__main__":
    main()
